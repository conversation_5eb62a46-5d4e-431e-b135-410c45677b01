#!/usr/bin/env python3
"""
测试百度功能
验证新增的百度自动化操作
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright


async def test_baidu_automation():
    """测试百度自动化功能"""
    print("测试百度自动化功能...")
    
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    
    playwright = None
    browser = None
    
    try:
        print("启动浏览器...")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            executable_path=chromium_path,
            headless=False
        )
        
        page = await browser.new_page()
        
        # 1. 访问百度
        print("1. 访问百度...")
        await page.goto("https://www.baidu.com", wait_until="networkidle")
        await asyncio.sleep(2)
        
        title = await page.title()
        print(f"✅ 成功打开百度: {title}")
        
        # 2. 查找并点击"新闻"
        print("2. 查找'新闻'链接...")
        
        news_selectors = [
            "text=新闻",
            "a:has-text('新闻')",
            "[href*='news']",
            "//a[contains(text(), '新闻')]"
        ]
        
        news_found = False
        for selector in news_selectors:
            try:
                print(f"   尝试选择器: {selector}")
                if selector.startswith("//"):
                    await page.wait_for_selector(f"xpath={selector}", timeout=3000)
                    await page.click(f"xpath={selector}")
                else:
                    await page.wait_for_selector(selector, timeout=3000)
                    await page.click(selector)
                
                print(f"✅ 成功点击'新闻' (选择器: {selector})")
                news_found = True
                break
            except Exception as e:
                print(f"   失败: {e}")
                continue
        
        if not news_found:
            print("❌ 无法找到'新闻'链接")
            return
        
        # 等待新闻页面加载
        await asyncio.sleep(3)
        
        # 3. 查找新闻列表
        print("3. 查找新闻列表...")
        
        news_items = await page.evaluate("""
            () => {
                const items = [];
                
                // 尝试多种可能的新闻列表选择器
                const selectors = [
                    '.news-item', '.result', '.c-container', 
                    '[data-click]', '.result-op', 'h3 a',
                    '.news-list li', '.hot-news li'
                ];
                
                for (const selector of selectors) {
                    const elements = document.querySelectorAll(selector);
                    console.log(`选择器 ${selector} 找到 ${elements.length} 个元素`);
                    
                    if (elements.length >= 3) {
                        elements.forEach((el, index) => {
                            const link = el.querySelector('a') || el;
                            const text = (el.innerText || el.textContent || '').trim();
                            
                            if (link && link.href && text) {
                                items.push({
                                    index: index,
                                    text: text.substring(0, 100),
                                    href: link.href,
                                    selector: selector
                                });
                            }
                        });
                        
                        if (items.length >= 3) break;
                    }
                }
                
                return items.slice(0, 10);
            }
        """)
        
        print(f"找到 {len(news_items)} 条新闻:")
        for i, item in enumerate(news_items[:5]):
            print(f"  [{i+1}] {item['text'][:60]}...")
        
        if len(news_items) >= 3:
            third_news = news_items[2]
            print(f"\n4. 准备打开第3条新闻:")
            print(f"   标题: {third_news['text'][:80]}...")
            print(f"   链接: {third_news['href']}")
            
            # 打开第3条新闻
            await page.goto(third_news['href'])
            await asyncio.sleep(3)
            
            print("✅ 成功打开第3条新闻")
            
            # 5. 测试元素标记功能
            print("5. 测试元素标记功能...")
            
            result = await page.evaluate("""
                () => {
                    // 移除之前的标记
                    const existingMarkers = document.querySelectorAll('.element-marker');
                    existingMarkers.forEach(marker => marker.remove());
                    
                    // 添加CSS样式
                    const style = document.createElement('style');
                    style.textContent = `
                        .element-marker {
                            position: absolute;
                            border: 2px solid red;
                            background-color: rgba(255, 0, 0, 0.1);
                            pointer-events: none;
                            z-index: 10000;
                            box-sizing: border-box;
                        }
                        
                        .element-number {
                            position: absolute;
                            top: -15px;
                            left: -2px;
                            background-color: red;
                            color: white;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 2px 6px;
                            border-radius: 3px;
                            min-width: 20px;
                            text-align: center;
                            font-family: Arial, sans-serif;
                        }
                    `;
                    document.head.appendChild(style);
                    
                    // 查找需要标记的元素
                    const elementsToMark = [];
                    
                    // 标题元素
                    const titles = document.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"]');
                    titles.forEach(el => {
                        if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                            elementsToMark.push({element: el, type: 'title'});
                        }
                    });
                    
                    // 图片元素
                    const images = document.querySelectorAll('img');
                    images.forEach(el => {
                        if (el.offsetWidth > 50 && el.offsetHeight > 50) {
                            elementsToMark.push({element: el, type: 'image'});
                        }
                    });
                    
                    // 链接元素
                    const links = document.querySelectorAll('a');
                    links.forEach(el => {
                        const text = (el.innerText || '').trim();
                        if (text && el.offsetWidth > 0 && el.offsetHeight > 0 && text.length > 3) {
                            elementsToMark.push({element: el, type: 'link'});
                        }
                    });
                    
                    // 限制标记数量
                    const maxMarkers = 30;
                    const selectedElements = elementsToMark.slice(0, maxMarkers);
                    
                    // 为每个元素添加标记
                    selectedElements.forEach((item, index) => {
                        const element = item.element;
                        const rect = element.getBoundingClientRect();
                        
                        // 创建红框标记
                        const marker = document.createElement('div');
                        marker.className = 'element-marker';
                        marker.style.left = (rect.left + window.scrollX) + 'px';
                        marker.style.top = (rect.top + window.scrollY) + 'px';
                        marker.style.width = rect.width + 'px';
                        marker.style.height = rect.height + 'px';
                        
                        // 创建序号标记
                        const number = document.createElement('div');
                        number.className = 'element-number';
                        number.textContent = index + 1;
                        marker.appendChild(number);
                        
                        document.body.appendChild(marker);
                    });
                    
                    return {
                        totalElements: selectedElements.length,
                        types: {
                            title: selectedElements.filter(item => item.type === 'title').length,
                            image: selectedElements.filter(item => item.type === 'image').length,
                            link: selectedElements.filter(item => item.type === 'link').length
                        }
                    };
                }
            """)
            
            print(f"✅ 成功标记 {result['totalElements']} 个元素")
            print(f"   标题: {result['types']['title']} 个")
            print(f"   图片: {result['types']['image']} 个")
            print(f"   链接: {result['types']['link']} 个")
            
        else:
            print(f"❌ 新闻条数不足，只找到 {len(news_items)} 条")
        
        print("\n🎉 测试完成！浏览器将保持打开状态供您查看效果...")
        print("按 Ctrl+C 退出")
        
        # 保持浏览器打开
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断，关闭浏览器...")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()


def main():
    """主函数"""
    print("百度自动化功能测试")
    print("=" * 50)
    print("这个测试将:")
    print("1. 打开百度首页")
    print("2. 点击'新闻'链接")
    print("3. 查找热点新闻列表")
    print("4. 打开第3条新闻")
    print("5. 给页面元素添加红框和序号标记")
    print("=" * 50)
    
    try:
        asyncio.run(test_baidu_automation())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
