# 分步操作功能说明

## 功能概述

根据您的要求，我已经将原来的自动化流程分解为多个独立的步骤，每个步骤对应一个按钮，用户可以按需执行。

## 界面布局

### 第一行按钮
- **打开浏览器** - 只启动浏览器，不做任何操作
- **关闭浏览器** - 关闭浏览器实例
- **打开网页** - 访问指定网址并标记元素
- **打开百度** - 百度自动化流程（保留原功能）

### 第二行按钮
- **新建** - 点击网页上的"新建"按钮
- **新建空白页** - 点击"新建空白页"选项
- **保存** - 填写表单并保存

## 详细操作流程

### 🔧 1. 打开浏览器
**功能**: 只启动Chromium浏览器，不访问任何网页
**操作**: 点击"打开浏览器"按钮
**结果**: 
- 启动指定路径的Chromium浏览器
- 显示空白页面
- 准备接受后续操作

### 🌐 2. 打开网页
**前提**: 必须先点击"打开浏览器"
**功能**: 访问指定的ZTE网址并标记所有元素
**操作**: 点击"打开网页"按钮
**结果**:
- 访问: `https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view`
- 智能等待页面加载完成
- 给所有重要元素添加红框和序号标记
- 在界面上显示页面信息（标题、URL、内容预览）

#### 标记的元素类型:
- **标题**: h1, h2, h3, h4, h5, h6, .title, [class*="title"]
- **图片**: img（尺寸 > 30x30像素）
- **链接**: a（有文本内容）
- **按钮**: button, input[type="button"], [role="button"]

#### 显示的页面信息:
```
网页标题: [页面标题]
当前URL: [完整URL]
页面内容长度: [字符数] 字符
页面内容预览:
[前500字符内容...]
```

### 🆕 3. 新建
**前提**: 必须先打开浏览器和网页
**功能**: 在当前网页上查找并点击"新建"按钮
**操作**: 点击"新建"按钮
**查找策略**:
```python
new_selectors = [
    "text=新建",
    "button:has-text('新建')",
    "[title*='新建']",
    "[aria-label*='新建']",
    "//button[contains(text(), '新建')]",
    "//span[contains(text(), '新建')]",
    "//div[contains(text(), '新建')]",
    "//*[contains(text(), '新建')]"
]
```

### 📄 4. 新建空白页
**前提**: 必须先执行"新建"操作
**功能**: 在弹出的菜单中点击"新建空白页"
**操作**: 点击"新建空白页"按钮
**查找策略**:
```python
blank_selectors = [
    "text=新建空白页",
    "button:has-text('新建空白页')",
    "[title*='新建空白页']",
    "//button[contains(text(), '新建空白页')]",
    "//div[contains(text(), '新建空白页')]",
    "//span[contains(text(), '新建空白页')]",
    "//*[contains(text(), '空白页')]"
]
```

### 💾 5. 保存
**前提**: 必须先执行"新建空白页"操作
**功能**: 自动填写表单并保存
**操作**: 点击"保存"按钮
**执行步骤**:
1. **输入标题**: "02浏览器操作测试网页新建"
2. **输入正文**: "测试文字：hello！"
3. **点击保存按钮**

#### 表单查找策略:
```python
# 标题输入框
title_selectors = [
    "input[placeholder*='标题']",
    "input[placeholder*='请输入标题']",
    "[data-testid*='title']",
    ".title-input",
    "input[name*='title']",
    "//input[contains(@placeholder, '标题')]"
]

# 正文输入框
content_selectors = [
    "textarea[placeholder*='正文']",
    "textarea[placeholder*='请输入正文']",
    "[data-testid*='content']",
    ".content-input",
    "textarea[name*='content']",
    "//textarea[contains(@placeholder, '正文')]",
    ".ql-editor",  # 富文本编辑器
    "[contenteditable='true']"  # 可编辑div
]

# 保存按钮
save_selectors = [
    "button:has-text('保存')",
    "text=保存",
    "[title*='保存']",
    "//button[contains(text(), '保存')]",
    "[data-testid*='save']",
    ".save-btn",
    "button[type='submit']"
]
```

## 使用方法

### 🚀 完整操作流程

1. **启动程序**:
   ```bash
   python simple_browser_automation.py
   ```

2. **按顺序点击按钮**:
   ```
   打开浏览器 → 打开网页 → 新建 → 新建空白页 → 保存
   ```

3. **观察每步结果**:
   - 每个操作都有详细的日志反馈
   - 可以在任何步骤暂停或重试
   - 错误时会显示具体的失败原因

### 🧪 测试验证

运行测试脚本验证功能:
```bash
python test_new_features.py
```

测试脚本会:
- 模拟完整的操作流程
- 显示每个步骤的详细信息
- 验证元素标记功能
- 检查"新建"按钮的可用性

## 技术特点

### 🔍 智能元素查找
- **多重选择器策略** - 每个操作都有多个备选方案
- **XPath和CSS选择器结合** - 提高查找成功率
- **文本内容匹配** - 支持模糊匹配
- **属性匹配** - 通过title、aria-label等属性查找

### 🎯 错误处理
- **前置条件检查** - 确保浏览器和页面已准备好
- **操作失败提示** - 清晰的错误信息
- **智能重试机制** - 自动尝试多种查找方法
- **用户友好反馈** - 详细的操作状态显示

### ⚡ 性能优化
- **异步操作** - 不阻塞界面
- **智能等待** - 根据页面加载情况调整等待时间
- **资源管理** - 正确的浏览器生命周期管理
- **内存优化** - 及时清理不需要的资源

## 预期日志输出

### 成功流程示例:
```
[14:30:10] 正在启动浏览器，请稍候...
[14:30:12] ✅ 浏览器已启动，等待进一步操作

[14:30:15] 正在打开网页，请稍候...
[14:30:16] 正在访问网址: https://i.zte.com.cn/...
[14:30:20] ✅ 页面内容已加载完成
[14:30:21] ✅ 成功标记 45 个元素
[14:30:21] 📊 元素类型统计:
[14:30:21]    标题: 8 个
[14:30:21]    图片: 12 个
[14:30:21]    链接: 20 个
[14:30:21]    按钮: 5 个

[14:30:25] 正在点击'新建'...
[14:30:26] ✅ 成功点击'新建' (选择器: text=新建)

[14:30:30] 正在点击'新建空白页'...
[14:30:31] ✅ 成功点击'新建空白页' (选择器: text=新建空白页)

[14:30:35] 正在填写表单并保存...
[14:30:36] ✅ 标题输入成功
[14:30:37] ✅ 正文输入成功
[14:30:38] ✅ 保存成功
[14:30:39] 🎉 表单填写和保存完成！
```

## 优势特点

1. **灵活性** - 用户可以选择执行特定步骤
2. **可控性** - 每个操作都可以单独验证
3. **调试友好** - 详细的状态反馈便于问题定位
4. **容错性** - 单个步骤失败不影响其他操作
5. **可扩展性** - 易于添加新的操作步骤

这个分步操作设计完全满足您的需求，提供了更好的用户控制和操作透明度！
