# 安装指南

## 系统要求

- Windows 10/11
- Python 3.8 或更高版本
- 指定的Chromium浏览器路径存在

## 安装步骤

### 1. 检查Python环境

打开命令提示符或PowerShell，运行：
```bash
python --version
```

如果没有Python，请从 https://python.org 下载安装。

### 2. 安装依赖包

在项目目录下运行：
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install PySide6>=6.5.0
pip install playwright>=1.40.0
pip install PyYAML>=6.0
```

### 3. 安装Playwright浏览器

```bash
playwright install chromium
```

### 4. 验证Chromium路径

确保以下路径存在：
```
C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe
```

如果路径不同，请修改 `browser_automation.py` 中的 `chromium_path` 变量。

### 5. 运行程序

方法1 - 直接运行：
```bash
python browser_automation.py
```

方法2 - 使用批处理文件：
```bash
run.bat
```

方法3 - 先检查环境：
```bash
python test_example.py
```

## 常见问题

### Q: 提示找不到Chromium浏览器
A: 运行 `playwright install chromium` 安装浏览器，或修改代码中的路径

### Q: 界面无法显示
A: 确保安装了PySide6：`pip install PySide6`

### Q: 记录功能不工作
A: 确保configs文件夹存在，程序会自动创建

### Q: 重放操作失败
A: 检查记录的YAML文件格式是否正确，确保目标网页元素存在

## 文件说明

- `browser_automation.py` - 主程序
- `requirements.txt` - 依赖列表
- `run.bat` - Windows启动脚本
- `test_example.py` - 环境检查脚本
- `configs/` - 存储记录文件的目录
- `README.md` - 使用说明
- `INSTALL.md` - 本安装指南
