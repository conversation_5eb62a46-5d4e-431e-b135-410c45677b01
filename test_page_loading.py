#!/usr/bin/env python3
"""
测试页面加载改进
专门验证页面是否完全加载
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright


async def test_page_loading():
    """测试页面加载过程"""
    print("测试页面加载过程...")
    
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    url = "https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view"
    
    playwright = None
    browser = None
    
    try:
        print("启动浏览器...")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            executable_path=chromium_path,
            headless=False
        )
        
        page = await browser.new_page()
        
        print(f"访问网址: {url}")
        await page.goto(url, wait_until="networkidle")
        
        print("开始监控页面内容变化...")
        
        # 监控页面内容变化
        for i in range(30):  # 监控30秒
            content = await page.evaluate("() => document.body.innerText || ''")
            content_length = len(content.strip())
            
            # 获取页面标题
            title = await page.title()
            
            # 检查是否有加载指示器
            loading_elements = await page.evaluate("""
                () => {
                    const loadingSelectors = ['.loading', '.spinner', '.el-loading', '[data-loading]', '.ant-spin'];
                    let hasLoading = false;
                    
                    loadingSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length > 0) {
                            hasLoading = true;
                        }
                    });
                    
                    return hasLoading;
                }
            """)
            
            # 检查是否有"新建"相关元素
            new_elements_count = await page.evaluate("""
                () => {
                    const allElements = document.querySelectorAll('*');
                    let count = 0;
                    
                    allElements.forEach(el => {
                        const text = (el.innerText || el.textContent || '').trim();
                        if (text.includes('新建') || text.includes('新增') || text.includes('创建')) {
                            count++;
                        }
                    });
                    
                    return count;
                }
            """)
            
            print(f"[{i+1:2d}秒] 内容长度: {content_length:4d} | 标题: {title[:30]} | 加载中: {loading_elements} | 新建元素: {new_elements_count}")
            
            # 显示内容预览（每5秒一次）
            if (i + 1) % 5 == 0:
                preview = content[:200] + "..." if len(content) > 200 else content
                print(f"     内容预览: {preview}")
            
            # 如果内容稳定且足够多，可以提前结束
            if content_length > 100 and new_elements_count > 0:
                print(f"✅ 检测到足够的页面内容和'新建'元素，页面可能已完全加载")
                break
            
            await asyncio.sleep(1)
        
        # 最终分析
        print("\n=== 最终页面分析 ===")
        
        final_content = await page.evaluate("() => document.body.innerText || ''")
        print(f"最终内容长度: {len(final_content)} 字符")
        
        if len(final_content) > 0:
            print("最终内容预览:")
            print(final_content[:500])
        
        # 查找所有可点击元素
        clickable_elements = await page.evaluate("""
            () => {
                const elements = [];
                const clickableSelectors = 'button, [role="button"], .btn, [onclick], a, .clickable, [data-testid], .el-button';
                const clickableElements = document.querySelectorAll(clickableSelectors);
                
                clickableElements.forEach((el, index) => {
                    const text = (el.innerText || el.textContent || el.title || el.getAttribute('aria-label') || '').trim();
                    if (text || el.className || el.id) {
                        elements.push({
                            index: index,
                            text: text,
                            tagName: el.tagName,
                            className: el.className || '',
                            id: el.id || ''
                        });
                    }
                });
                
                return elements.slice(0, 20);
            }
        """)
        
        print(f"\n找到 {len(clickable_elements)} 个可点击元素:")
        for element in clickable_elements:
            print(f"  [{element['index']}] {element['tagName']}: '{element['text'][:50]}'")
            if element['className']:
                print(f"      class: {element['className']}")
            if element['id']:
                print(f"      id: {element['id']}")
        
        # 专门查找"新建"相关元素
        new_elements = await page.evaluate("""
            () => {
                const elements = [];
                const allElements = document.querySelectorAll('*');
                
                allElements.forEach((el, index) => {
                    const text = (el.innerText || el.textContent || '').trim();
                    if (text.includes('新建') || text.includes('新增') || text.includes('创建') || text.includes('添加')) {
                        const rect = el.getBoundingClientRect();
                        elements.push({
                            index: index,
                            text: text,
                            tagName: el.tagName,
                            className: el.className || '',
                            id: el.id || '',
                            isVisible: rect.width > 0 && rect.height > 0
                        });
                    }
                });
                
                return elements.filter(el => el.isVisible).slice(0, 10);
            }
        """)
        
        print(f"\n找到 {len(new_elements)} 个包含'新建'的可见元素:")
        for element in new_elements:
            print(f"  [{element['index']}] {element['tagName']}: '{element['text'][:50]}'")
            if element['className']:
                print(f"      class: {element['className']}")
            if element['id']:
                print(f"      id: {element['id']}")
        
        # 检查是否需要登录
        login_check = await page.evaluate("""
            () => {
                const text = document.body.innerText || '';
                const hasLoginText = text.includes('登录') || text.includes('login') || 
                                   text.includes('用户名') || text.includes('密码');
                
                const loginInputs = document.querySelectorAll('input[type="password"], input[name*="password"]');
                
                return {
                    hasLoginText: hasLoginText,
                    hasLoginInputs: loginInputs.length > 0,
                    pageTitle: document.title || ''
                };
            }
        """)
        
        if login_check['hasLoginText'] or login_check['hasLoginInputs']:
            print(f"\n⚠️ 检测到登录页面")
            print(f"页面标题: {login_check['pageTitle']}")
            print("建议: 请先手动登录，然后再运行自动化程序")
        else:
            print(f"\n✅ 未检测到登录页面，可以继续自动化操作")
        
        print("\n测试完成，浏览器将保持打开状态供您检查...")
        print("按 Ctrl+C 退出")
        
        # 保持浏览器打开
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断，关闭浏览器...")
        
    except Exception as e:
        print(f"测试出错: {e}")
    
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()


def main():
    """主函数"""
    print("页面加载测试工具")
    print("=" * 50)
    print("这个工具将:")
    print("1. 打开指定的ZTE网址")
    print("2. 监控页面内容变化")
    print("3. 查找'新建'相关元素")
    print("4. 检查是否需要登录")
    print("5. 分析页面加载状态")
    print("=" * 50)
    
    try:
        asyncio.run(test_page_loading())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
