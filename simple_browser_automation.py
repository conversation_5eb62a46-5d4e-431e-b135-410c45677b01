import sys
import asyncio
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QTextEdit, 
                               QSpinBox, QLabel, QMessageBox)
from PySide6.QtCore import QThread, Signal, QObject
from playwright.async_api import async_playwright


class BrowserWorker(QObject):
    """浏览器操作的工作线程"""
    message_signal = Signal(str)
    error_signal = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.browser = None
        self.page = None
        self.playwright = None
        
    async def open_browser_and_operate(self, url: str, wait_seconds: int):
        """打开浏览器并执行操作"""
        try:
            self.message_signal.emit("正在启动浏览器...")
            
            # 指定Chromium路径
            chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
            
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                executable_path=chromium_path,
                headless=False
            )
            
            self.page = await self.browser.new_page()
            
            self.message_signal.emit(f"正在访问网址: {url}")
            await self.page.goto(url, wait_until="networkidle")

            self.message_signal.emit(f"等待页面完全加载...")

            # 等待页面基本加载
            await asyncio.sleep(wait_seconds)

            # 等待页面内容渲染完成
            self.message_signal.emit("检查页面内容是否加载完成...")

            # 多次检查页面内容，直到内容稳定
            previous_content_length = 0
            stable_count = 0
            max_wait_time = 30  # 最多等待30秒

            for i in range(max_wait_time):
                current_content = await self.page.evaluate("() => document.body.innerText || ''")
                current_length = len(current_content.strip())

                self.message_signal.emit(f"第{i+1}次检查: 页面内容长度 {current_length} 字符")

                if current_length > 100:  # 内容足够多
                    if current_length == previous_content_length:
                        stable_count += 1
                        if stable_count >= 3:  # 连续3次长度相同，认为加载完成
                            self.message_signal.emit(f"✅ 页面内容已稳定，长度: {current_length} 字符")
                            break
                    else:
                        stable_count = 0
                        previous_content_length = current_length

                await asyncio.sleep(1)
            else:
                self.message_signal.emit("⚠️ 等待超时，但继续执行...")

            # 额外等待确保所有动态内容加载
            self.message_signal.emit("等待动态内容加载...")
            await asyncio.sleep(3)
            
            # 读取网页信息
            title = await self.page.title()
            url_current = self.page.url
            
            # 再次确认页面内容已加载
            self.message_signal.emit("最终检查页面内容...")

            # 等待特定元素出现，表明页面已加载
            try:
                # 等待常见的页面元素出现
                await self.page.wait_for_selector("body", timeout=10000)
                await asyncio.sleep(2)

                # 尝试等待可能的加载指示器消失
                loading_selectors = [
                    ".loading", ".spinner", ".el-loading",
                    "[data-loading]", ".ant-spin"
                ]

                for selector in loading_selectors:
                    try:
                        await self.page.wait_for_selector(selector, state="hidden", timeout=3000)
                        self.message_signal.emit(f"✅ 加载指示器已消失: {selector}")
                    except:
                        continue

            except Exception as e:
                self.message_signal.emit(f"等待页面元素时出错: {e}")

            # 获取详细的页面信息
            self.message_signal.emit("开始分析页面内容...")

            # 获取页面HTML结构信息
            page_info = await self.page.evaluate("""
                () => {
                    // 获取所有文本内容
                    const allText = document.body.innerText || document.body.textContent || '';

                    // 获取所有按钮和可点击元素
                    const clickableElements = [];
                    const buttons = document.querySelectorAll('button, [role="button"], .btn, [onclick], a, .clickable, [data-testid], .el-button');

                    buttons.forEach((el, index) => {
                        const text = (el.innerText || el.textContent || el.title || el.getAttribute('aria-label') || '').trim();
                        const classes = el.className || '';
                        const id = el.id || '';
                        const tagName = el.tagName || '';
                        const dataTestId = el.getAttribute('data-testid') || '';
                        const role = el.getAttribute('role') || '';

                        clickableElements.push({
                            index: index,
                            text: text,
                            classes: classes,
                            id: id,
                            tagName: tagName,
                            dataTestId: dataTestId,
                            role: role
                        });
                    });

                    // 查找包含"新建"的元素
                    const newElements = [];
                    document.querySelectorAll('*').forEach((el, index) => {
                        const text = (el.innerText || el.textContent || '').trim();
                        if (text.includes('新建') || text.includes('新增') || text.includes('创建') || text.includes('添加')) {
                            newElements.push({
                                index: index,
                                text: text,
                                tagName: el.tagName,
                                classes: el.className || '',
                                id: el.id || '',
                                outerHTML: el.outerHTML.substring(0, 200)
                            });
                        }
                    });

                    return {
                        allText: allText,
                        clickableElements: clickableElements.slice(0, 30),
                        newElements: newElements.slice(0, 10)
                    };
                }
            """)

            # 显示详细信息
            info = f"""
网页标题: {title}
当前URL: {url_current}

=== 完整页面内容 ===
{page_info['allText']}

=== 页面内容长度 ===
总字符数: {len(page_info['allText'])}
            """

            self.message_signal.emit(info)

            # 显示所有可点击元素
            self.message_signal.emit("=== 所有可点击元素详情 ===")
            for element in page_info['clickableElements']:
                element_info = f"[{element['index']}] {element['tagName']}: '{element['text']}'"
                if element['classes']:
                    element_info += f" (class: {element['classes']})"
                if element['id']:
                    element_info += f" (id: {element['id']})"
                if element['dataTestId']:
                    element_info += f" (data-testid: {element['dataTestId']})"
                if element['role']:
                    element_info += f" (role: {element['role']})"
                self.message_signal.emit(element_info)

            # 显示包含"新建"的元素
            self.message_signal.emit("=== 包含'新建'相关文字的元素 ===")
            for element in page_info['newElements']:
                element_info = f"[{element['index']}] {element['tagName']}: '{element['text']}'"
                if element['classes']:
                    element_info += f" (class: {element['classes']})"
                if element['id']:
                    element_info += f" (id: {element['id']})"
                self.message_signal.emit(element_info)
                self.message_signal.emit(f"  HTML: {element['outerHTML']}")

            self.message_signal.emit("=== 页面分析完成 ===")
            
            # 开始执行自动化操作
            await self.perform_automation()
            
        except Exception as e:
            self.error_signal.emit(f"打开浏览器失败: {str(e)}")
    
    async def perform_automation(self):
        """执行自动化操作"""
        try:
            self.message_signal.emit("开始执行自动化操作...")

            # 确保页面完全加载和渲染
            self.message_signal.emit("确保页面完全渲染...")

            # 等待页面稳定
            await asyncio.sleep(5)

            # 再次检查页面内容
            current_content = await self.page.evaluate("() => document.body.innerText || ''")
            content_length = len(current_content.strip())

            self.message_signal.emit(f"当前页面内容长度: {content_length} 字符")

            if content_length < 50:
                self.message_signal.emit("⚠️ 页面内容仍然很少，继续等待...")

                # 尝试刷新页面
                await self.page.reload(wait_until="networkidle")
                await asyncio.sleep(5)

                # 再次检查
                current_content = await self.page.evaluate("() => document.body.innerText || ''")
                content_length = len(current_content.strip())
                self.message_signal.emit(f"刷新后页面内容长度: {content_length} 字符")

            # 显示当前页面内容用于调试
            if content_length > 0:
                preview = current_content[:500] + "..." if len(current_content) > 500 else current_content
                self.message_signal.emit(f"当前页面内容预览:\n{preview}")

            # 检查页面URL是否正确
            current_url = self.page.url
            self.message_signal.emit(f"当前页面URL: {current_url}")

            # 检查是否需要登录
            login_indicators = await self.page.evaluate("""
                () => {
                    const text = document.body.innerText || '';
                    const hasLogin = text.includes('登录') || text.includes('login') ||
                                   text.includes('用户名') || text.includes('密码') ||
                                   text.includes('账号') || text.includes('验证码');

                    const loginElements = document.querySelectorAll('input[type="password"], input[name*="password"], input[name*="login"]');

                    return {
                        hasLoginText: hasLogin,
                        hasLoginInputs: loginElements.length > 0,
                        pageTitle: document.title || ''
                    };
                }
            """)

            if login_indicators['hasLoginText'] or login_indicators['hasLoginInputs']:
                self.error_signal.emit("❌ 检测到登录页面，请先登录后再运行程序")
                self.message_signal.emit(f"页面标题: {login_indicators['pageTitle']}")
                return

            self.message_signal.emit("✅ 页面检查完成，开始查找元素...")
            
            # 1. 智能查找并点击"新建"按钮
            self.message_signal.emit("🔍 开始智能查找'新建'按钮...")

            # 首先等待页面完全加载
            await asyncio.sleep(3)

            # 获取页面上所有包含"新建"文字的元素
            new_elements = await self.page.evaluate("""
                () => {
                    const elements = [];
                    const allElements = document.querySelectorAll('*');

                    allElements.forEach((el, index) => {
                        const text = (el.innerText || el.textContent || el.title || el.getAttribute('aria-label') || '').trim();

                        // 查找包含"新建"、"新增"、"创建"、"添加"等关键词的元素
                        if (text.includes('新建') || text.includes('新增') || text.includes('创建') || text.includes('添加')) {
                            const rect = el.getBoundingClientRect();

                            elements.push({
                                index: index,
                                text: text,
                                tagName: el.tagName,
                                className: el.className || '',
                                id: el.id || '',
                                isVisible: rect.width > 0 && rect.height > 0,
                                isClickable: el.tagName === 'BUTTON' || el.tagName === 'A' ||
                                            el.getAttribute('role') === 'button' ||
                                            el.onclick !== null ||
                                            el.className.includes('btn') ||
                                            el.className.includes('button') ||
                                            el.className.includes('clickable'),
                                x: rect.x,
                                y: rect.y,
                                width: rect.width,
                                height: rect.height,
                                outerHTML: el.outerHTML.substring(0, 300)
                            });
                        }
                    });

                    // 按可见性和可点击性排序
                    return elements.sort((a, b) => {
                        if (a.isVisible && !b.isVisible) return -1;
                        if (!a.isVisible && b.isVisible) return 1;
                        if (a.isClickable && !b.isClickable) return -1;
                        if (!a.isClickable && b.isClickable) return 1;
                        return 0;
                    });
                }
            """)

            self.message_signal.emit(f"📋 找到 {len(new_elements)} 个包含'新建'相关文字的元素:")

            for i, element in enumerate(new_elements[:10]):  # 只显示前10个
                status = "✅可见" if element['isVisible'] else "❌隐藏"
                clickable = "🖱️可点击" if element['isClickable'] else "🚫不可点击"
                self.message_signal.emit(f"  [{i}] {element['tagName']}: '{element['text'][:50]}' {status} {clickable}")
                if element['className']:
                    self.message_signal.emit(f"      class: {element['className']}")
                if element['id']:
                    self.message_signal.emit(f"      id: {element['id']}")
                self.message_signal.emit(f"      位置: ({element['x']:.0f}, {element['y']:.0f}) 大小: {element['width']:.0f}x{element['height']:.0f}")

            # 尝试点击找到的元素
            new_button_found = False

            # 优先尝试可见且可点击的元素
            for element in new_elements:
                if element['isVisible'] and element['isClickable']:
                    try:
                        self.message_signal.emit(f"🎯 尝试点击: {element['tagName']} '{element['text'][:30]}'")

                        # 尝试多种点击方式
                        if element['id']:
                            await self.page.click(f"#{element['id']}")
                            self.message_signal.emit(f"✅ 通过ID成功点击: #{element['id']}")
                        elif element['className']:
                            # 尝试第一个类名
                            first_class = element['className'].split()[0]
                            await self.page.click(f".{first_class}")
                            self.message_signal.emit(f"✅ 通过类名成功点击: .{first_class}")
                        else:
                            # 通过坐标点击
                            await self.page.mouse.click(element['x'] + element['width']/2, element['y'] + element['height']/2)
                            self.message_signal.emit(f"✅ 通过坐标成功点击: ({element['x'] + element['width']/2:.0f}, {element['y'] + element['height']/2:.0f})")

                        new_button_found = True
                        break

                    except Exception as e:
                        self.message_signal.emit(f"❌ 点击失败: {str(e)}")
                        continue

            # 如果还是没找到，尝试更广泛的搜索
            if not new_button_found:
                self.message_signal.emit("🔄 尝试更广泛的元素搜索...")

                # 尝试所有可能的选择器
                selectors_to_try = [
                    "text=新建",
                    "button:has-text('新建')",
                    "[title*='新建']",
                    "[aria-label*='新建']",
                    "//button[contains(text(), '新建')]",
                    "//span[contains(text(), '新建')]",
                    "//div[contains(text(), '新建')]",
                    "//a[contains(text(), '新建')]",
                    "//*[contains(text(), '新建')]",
                    ".el-button:has-text('新建')",
                    ".ant-btn:has-text('新建')",
                    "[data-testid*='new']",
                    "[data-testid*='create']",
                    "[data-testid*='add']"
                ]

                for selector in selectors_to_try:
                    try:
                        self.message_signal.emit(f"🎯 尝试选择器: {selector}")

                        if selector.startswith("//") or selector.startswith("//*"):
                            await self.page.wait_for_selector(f"xpath={selector}", timeout=2000)
                            await self.page.click(f"xpath={selector}")
                        else:
                            await self.page.wait_for_selector(selector, timeout=2000)
                            await self.page.click(selector)

                        self.message_signal.emit(f"✅ 成功点击'新建'按钮 (选择器: {selector})")
                        new_button_found = True
                        break

                    except Exception as e:
                        continue

            if not new_button_found:
                self.error_signal.emit("❌ 尝试了所有方法仍无法找到'新建'按钮")
                self.message_signal.emit("💡 建议检查:")
                self.message_signal.emit("  1. 页面是否完全加载")
                self.message_signal.emit("  2. 是否需要登录")
                self.message_signal.emit("  3. '新建'按钮是否在其他位置或名称不同")
                return
            
            # 等待菜单出现
            await asyncio.sleep(3)

            # 2. 智能查找并点击"新建空白页"
            self.message_signal.emit("🔍 查找'新建空白页'选项...")

            # 查找包含"空白页"、"空白"、"页面"等关键词的元素
            blank_page_elements = await self.page.evaluate("""
                () => {
                    const elements = [];
                    const allElements = document.querySelectorAll('*');

                    allElements.forEach((el, index) => {
                        const text = (el.innerText || el.textContent || '').trim();

                        if (text.includes('空白页') || text.includes('空白') ||
                            (text.includes('页面') && text.includes('新建')) ||
                            text.includes('blank') || text.includes('page')) {
                            const rect = el.getBoundingClientRect();

                            elements.push({
                                index: index,
                                text: text,
                                tagName: el.tagName,
                                className: el.className || '',
                                id: el.id || '',
                                isVisible: rect.width > 0 && rect.height > 0,
                                isClickable: el.tagName === 'BUTTON' || el.tagName === 'A' ||
                                            el.getAttribute('role') === 'button' ||
                                            el.onclick !== null ||
                                            el.className.includes('btn') ||
                                            el.className.includes('menu') ||
                                            el.className.includes('item'),
                                x: rect.x,
                                y: rect.y
                            });
                        }
                    });

                    return elements.filter(el => el.isVisible);
                }
            """)

            self.message_signal.emit(f"📋 找到 {len(blank_page_elements)} 个相关元素")

            blank_page_found = False
            for element in blank_page_elements:
                try:
                    self.message_signal.emit(f"🎯 尝试点击: '{element['text'][:50]}'")

                    if element['id']:
                        await self.page.click(f"#{element['id']}")
                    elif element['className']:
                        first_class = element['className'].split()[0]
                        await self.page.click(f".{first_class}")
                    else:
                        await self.page.mouse.click(element['x'] + 10, element['y'] + 10)

                    self.message_signal.emit("✅ 成功点击'新建空白页'")
                    blank_page_found = True
                    break

                except Exception as e:
                    continue

            # 如果还没找到，尝试通用选择器
            if not blank_page_found:
                selectors = [
                    "text=新建空白页", "text=空白页", "text=空白",
                    "//span[contains(text(), '空白')]",
                    "//div[contains(text(), '空白')]",
                    "//*[contains(text(), '空白页')]"
                ]

                for selector in selectors:
                    try:
                        if selector.startswith("//"):
                            await self.page.click(f"xpath={selector}")
                        else:
                            await self.page.click(selector)
                        self.message_signal.emit(f"✅ 通过选择器找到: {selector}")
                        blank_page_found = True
                        break
                    except:
                        continue

            if not blank_page_found:
                self.error_signal.emit("❌ 无法找到'新建空白页'选项")
                return
            
            # 等待新页面加载
            await asyncio.sleep(3)
            
            # 3. 输入标题
            self.message_signal.emit("正在输入标题...")
            
            title_selectors = [
                "input[placeholder*='标题']",
                "input[placeholder*='请输入标题']",
                "[data-testid*='title']",
                ".title-input",
                "input[name*='title']",
                "//input[contains(@placeholder, '标题')]"
            ]
            
            title_input_found = False
            for selector in title_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.fill(f"xpath={selector}", "02浏览器操作测试网页新建")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.fill(selector, "02浏览器操作测试网页新建")
                    
                    self.message_signal.emit(f"✅ 成功输入标题 (选择器: {selector})")
                    title_input_found = True
                    break
                except Exception:
                    continue
            
            if not title_input_found:
                self.error_signal.emit("❌ 无法找到标题输入框")
                return
            
            # 4. 输入正文
            self.message_signal.emit("正在输入正文...")
            
            content_selectors = [
                "textarea[placeholder*='正文']",
                "textarea[placeholder*='请输入正文']",
                "[data-testid*='content']",
                ".content-input",
                "textarea[name*='content']",
                "//textarea[contains(@placeholder, '正文')]",
                ".ql-editor",  # Quill编辑器
                "[contenteditable='true']"  # 富文本编辑器
            ]
            
            content_input_found = False
            for selector in content_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        if "contenteditable" in selector:
                            await self.page.click(f"xpath={selector}")
                            await self.page.keyboard.type("测试文字：hello！")
                        else:
                            await self.page.fill(f"xpath={selector}", "测试文字：hello！")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        if "contenteditable" in selector:
                            await self.page.click(selector)
                            await self.page.keyboard.type("测试文字：hello！")
                        else:
                            await self.page.fill(selector, "测试文字：hello！")
                    
                    self.message_signal.emit(f"✅ 成功输入正文 (选择器: {selector})")
                    content_input_found = True
                    break
                except Exception:
                    continue
            
            if not content_input_found:
                self.error_signal.emit("❌ 无法找到正文输入框")
                return
            
            # 5. 点击保存
            self.message_signal.emit("正在查找保存按钮...")
            
            save_selectors = [
                "button:has-text('保存')",
                "text=保存",
                "[title*='保存']",
                "button[title*='保存']",
                "//button[contains(text(), '保存')]",
                "//button[@title='保存']",
                "[data-testid*='save']",
                ".save-btn",
                "button[type='submit']"
            ]
            
            save_found = False
            for selector in save_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)
                    
                    self.message_signal.emit(f"✅ 成功点击保存按钮 (选择器: {selector})")
                    save_found = True
                    break
                except Exception:
                    continue
            
            if not save_found:
                self.error_signal.emit("❌ 无法找到保存按钮")
                return
            
            # 等待保存完成
            await asyncio.sleep(2)
            
            self.message_signal.emit("🎉 自动化操作完成！")
            
        except Exception as e:
            self.error_signal.emit(f"自动化操作失败: {str(e)}")
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.message_signal.emit("浏览器已关闭")
            
        except Exception as e:
            self.error_signal.emit(f"关闭浏览器失败: {str(e)}")

    async def open_baidu_and_operate(self):
        """打开百度并执行自动化操作"""
        try:
            self.message_signal.emit("正在启动浏览器...")

            # 指定Chromium路径
            chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"

            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                executable_path=chromium_path,
                headless=False
            )

            self.page = await self.browser.new_page()

            # 1. 访问百度
            self.message_signal.emit("正在访问百度...")
            await self.page.goto("https://www.baidu.com", wait_until="networkidle")
            await asyncio.sleep(3)

            # 读取百度首页信息
            title = await self.page.title()
            self.message_signal.emit(f"✅ 成功打开百度首页: {title}")

            # 2. 给百度首页元素添加标记
            self.message_signal.emit("🎯 给百度首页元素添加标记...")
            await self.add_element_markers()

            # 等待用户查看标记效果
            await asyncio.sleep(3)

            # 3. 点击"新闻"
            self.message_signal.emit("🔍 查找'新闻'链接...")

            news_selectors = [
                "text=新闻",
                "a:has-text('新闻')",
                "[href*='news']",
                "//a[contains(text(), '新闻')]",
                "#s-top-left a:has-text('新闻')"
            ]

            news_found = False
            for selector in news_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)

                    self.message_signal.emit(f"✅ 成功点击'新闻' (选择器: {selector})")
                    news_found = True
                    break
                except Exception:
                    continue

            if not news_found:
                self.error_signal.emit("❌ 无法找到'新闻'链接")
                return

            # 等待新闻页面加载
            await asyncio.sleep(3)

            # 4. 点击"今日辟谣"
            self.message_signal.emit("🔍 查找'今日辟谣'...")

            rumor_selectors = [
                "text=今日辟谣",
                "a:has-text('今日辟谣')",
                "[href*='辟谣']",
                "//a[contains(text(), '今日辟谣')]",
                "//a[contains(text(), '辟谣')]",
                ".channel-item:has-text('今日辟谣')",
                "[title*='今日辟谣']"
            ]

            rumor_found = False
            for selector in rumor_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)

                    self.message_signal.emit(f"✅ 成功点击'今日辟谣' (选择器: {selector})")
                    rumor_found = True
                    break
                except Exception:
                    continue

            if not rumor_found:
                self.message_signal.emit("⚠️ 未找到'今日辟谣'，继续查找其他新闻...")

            # 等待页面加载
            await asyncio.sleep(3)

            # 5. 查找热点新闻第3条（如果没有找到今日辟谣）
            if not rumor_found:
                self.message_signal.emit("🔍 查找热点新闻第3条...")

            # 等待页面完全加载
            await self.page.wait_for_load_state("networkidle")

            # 查找新闻列表
            news_items = await self.page.evaluate("""
                () => {
                    const items = [];

                    // 尝试多种可能的新闻列表选择器
                    const selectors = [
                        '.news-item', '.result', '.c-container',
                        '[data-click]', '.result-op', 'h3 a',
                        '.news-list li', '.hot-news li'
                    ];

                    for (const selector of selectors) {
                        const elements = document.querySelectorAll(selector);
                        if (elements.length >= 3) {
                            elements.forEach((el, index) => {
                                const link = el.querySelector('a') || el;
                                const text = (el.innerText || el.textContent || '').trim();

                                if (link && link.href && text) {
                                    items.push({
                                        index: index,
                                        text: text.substring(0, 100),
                                        href: link.href,
                                        selector: selector
                                    });
                                }
                            });

                            if (items.length >= 3) break;
                        }
                    }

                    return items.slice(0, 10); // 返回前10条
                }
            """)

            if len(news_items) >= 3:
                third_news = news_items[2]  # 第3条（索引为2）
                self.message_signal.emit(f"📰 找到第3条新闻: {third_news['text'][:50]}...")

                # 点击第3条新闻
                await self.page.goto(third_news['href'])
                await asyncio.sleep(3)

                self.message_signal.emit("✅ 成功打开第3条新闻")

                # 4. 给网页元素标红框和序号（OpenManus风格）
                await self.add_element_markers()

            else:
                self.error_signal.emit(f"❌ 找到的新闻条数不足，只有 {len(news_items)} 条")

        except Exception as e:
            self.error_signal.emit(f"百度操作失败: {str(e)}")

    async def add_element_markers(self):
        """给网页元素添加红框和序号标记（OpenManus风格）"""
        try:
            self.message_signal.emit("🎯 开始给网页元素添加标记...")

            # 注入CSS样式和JavaScript代码
            await self.page.evaluate("""
                () => {
                    // 移除之前的标记
                    const existingMarkers = document.querySelectorAll('.element-marker');
                    existingMarkers.forEach(marker => marker.remove());

                    // 添加CSS样式
                    const style = document.createElement('style');
                    style.textContent = `
                        .element-marker {
                            position: absolute;
                            border: 2px solid red;
                            background-color: rgba(255, 0, 0, 0.1);
                            pointer-events: none;
                            z-index: 10000;
                            box-sizing: border-box;
                        }

                        .element-number {
                            position: absolute;
                            top: -15px;
                            left: -2px;
                            background-color: red;
                            color: white;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 2px 6px;
                            border-radius: 3px;
                            min-width: 20px;
                            text-align: center;
                            font-family: Arial, sans-serif;
                        }
                    `;
                    document.head.appendChild(style);

                    // 查找需要标记的元素
                    const elementsToMark = [];

                    // 标题元素
                    const titles = document.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"], [class*="headline"]');
                    titles.forEach(el => {
                        if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                            elementsToMark.push({element: el, type: 'title'});
                        }
                    });

                    // 图片元素
                    const images = document.querySelectorAll('img');
                    images.forEach(el => {
                        if (el.offsetWidth > 50 && el.offsetHeight > 50) {
                            elementsToMark.push({element: el, type: 'image'});
                        }
                    });

                    // 链接元素
                    const links = document.querySelectorAll('a');
                    links.forEach(el => {
                        const text = (el.innerText || '').trim();
                        if (text && el.offsetWidth > 0 && el.offsetHeight > 0 && text.length > 3) {
                            elementsToMark.push({element: el, type: 'link'});
                        }
                    });

                    // 按钮元素
                    const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"], [role="button"]');
                    buttons.forEach(el => {
                        if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                            elementsToMark.push({element: el, type: 'button'});
                        }
                    });

                    // 文本段落
                    const paragraphs = document.querySelectorAll('p, .content, [class*="content"], .text, [class*="text"]');
                    paragraphs.forEach(el => {
                        const text = (el.innerText || '').trim();
                        if (text && text.length > 20 && el.offsetWidth > 0 && el.offsetHeight > 0) {
                            elementsToMark.push({element: el, type: 'text'});
                        }
                    });

                    // 限制标记数量，避免页面过于混乱
                    const maxMarkers = 50;
                    const selectedElements = elementsToMark.slice(0, maxMarkers);

                    // 为每个元素添加标记
                    selectedElements.forEach((item, index) => {
                        const element = item.element;
                        const rect = element.getBoundingClientRect();

                        // 创建红框标记
                        const marker = document.createElement('div');
                        marker.className = 'element-marker';
                        marker.style.left = (rect.left + window.scrollX) + 'px';
                        marker.style.top = (rect.top + window.scrollY) + 'px';
                        marker.style.width = rect.width + 'px';
                        marker.style.height = rect.height + 'px';

                        // 创建序号标记
                        const number = document.createElement('div');
                        number.className = 'element-number';
                        number.textContent = index + 1;
                        marker.appendChild(number);

                        document.body.appendChild(marker);
                    });

                    return {
                        totalElements: selectedElements.length,
                        types: {
                            title: selectedElements.filter(item => item.type === 'title').length,
                            image: selectedElements.filter(item => item.type === 'image').length,
                            link: selectedElements.filter(item => item.type === 'link').length,
                            button: selectedElements.filter(item => item.type === 'button').length,
                            text: selectedElements.filter(item => item.type === 'text').length
                        }
                    };
                }
            """)

            # 获取标记结果
            result = await self.page.evaluate("() => window.markingResult || {}")

            if result:
                self.message_signal.emit(f"✅ 成功标记 {result.get('totalElements', 0)} 个元素")
                types = result.get('types', {})
                self.message_signal.emit(f"📊 元素类型统计:")
                self.message_signal.emit(f"   标题: {types.get('title', 0)} 个")
                self.message_signal.emit(f"   图片: {types.get('image', 0)} 个")
                self.message_signal.emit(f"   链接: {types.get('link', 0)} 个")
                self.message_signal.emit(f"   按钮: {types.get('button', 0)} 个")
                self.message_signal.emit(f"   文本: {types.get('text', 0)} 个")

            self.message_signal.emit("🎉 网页元素标记完成！")

        except Exception as e:
            self.error_signal.emit(f"添加元素标记失败: {str(e)}")


class BrowserThread(QThread):
    """浏览器操作线程"""
    def __init__(self, worker, operation, *args):
        super().__init__()
        self.worker = worker
        self.operation = operation
        self.args = args
    
    def run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            if self.operation == "open":
                loop.run_until_complete(self.worker.open_browser_and_operate(*self.args))
            elif self.operation == "close":
                loop.run_until_complete(self.worker.close_browser())
            elif self.operation == "baidu":
                loop.run_until_complete(self.worker.open_baidu_and_operate())
        finally:
            loop.close()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = BrowserWorker()
        self.current_thread = None
        
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简化浏览器自动化工具")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 等待时间设置
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("等待时间(秒):"))
        self.wait_time_spinbox = QSpinBox()
        self.wait_time_spinbox.setRange(1, 60)
        self.wait_time_spinbox.setValue(5)
        time_layout.addWidget(self.wait_time_spinbox)
        time_layout.addStretch()
        layout.addLayout(time_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()

        self.open_btn = QPushButton("打开浏览器")
        self.close_btn = QPushButton("关闭浏览器")
        self.baidu_btn = QPushButton("打开百度")

        button_layout.addWidget(self.open_btn)
        button_layout.addWidget(self.close_btn)
        button_layout.addWidget(self.baidu_btn)

        layout.addLayout(button_layout)
        
        # 信息显示区域
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        layout.addWidget(self.info_text)
        
        # 连接按钮事件
        self.open_btn.clicked.connect(self.open_browser)
        self.close_btn.clicked.connect(self.close_browser)
        self.baidu_btn.clicked.connect(self.open_baidu)
    
    def connect_signals(self):
        """连接信号"""
        self.worker.message_signal.connect(self.show_message)
        self.worker.error_signal.connect(self.show_error)
    
    def show_message(self, message):
        """显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] {message}")
    
    def show_error(self, error):
        """显示错误"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] 错误: {error}")
        QMessageBox.warning(self, "错误", error)
    
    def open_browser(self):
        """打开浏览器并执行自动化操作"""
        url = "https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view"
        wait_seconds = self.wait_time_spinbox.value()
        
        # 禁用按钮防止重复点击
        self.open_btn.setEnabled(False)
        self.show_message("正在启动浏览器，请稍候...")
        
        self.current_thread = BrowserThread(self.worker, "open", url, wait_seconds)
        self.current_thread.finished.connect(lambda: self.open_btn.setEnabled(True))
        self.current_thread.start()
    
    def close_browser(self):
        """关闭浏览器"""
        self.current_thread = BrowserThread(self.worker, "close")
        self.current_thread.start()

    def open_baidu(self):
        """打开百度并执行自动化操作"""
        # 禁用按钮防止重复点击
        self.baidu_btn.setEnabled(False)
        self.show_message("正在打开百度，请稍候...")

        self.current_thread = BrowserThread(self.worker, "baidu")
        self.current_thread.finished.connect(lambda: self.baidu_btn.setEnabled(True))
        self.current_thread.start()


def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
