import sys
import asyncio
from datetime import datetime
from pathlib import Path

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                               QHBoxLayout, QWidget, QPushButton, QTextEdit, 
                               QSpinBox, QLabel, QMessageBox)
from PySide6.QtCore import QThread, Signal, QObject
from playwright.async_api import async_playwright


class BrowserWorker(QObject):
    """浏览器操作的工作线程"""
    message_signal = Signal(str)
    error_signal = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.browser = None
        self.page = None
        self.playwright = None
        
    async def open_browser_and_operate(self, url: str, wait_seconds: int):
        """打开浏览器并执行操作"""
        try:
            self.message_signal.emit("正在启动浏览器...")
            
            # 指定Chromium路径
            chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
            
            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                executable_path=chromium_path,
                headless=False
            )
            
            self.page = await self.browser.new_page()
            
            self.message_signal.emit(f"正在访问网址: {url}")
            await self.page.goto(url)
            
            self.message_signal.emit(f"等待 {wait_seconds} 秒...")
            await asyncio.sleep(wait_seconds)
            
            # 读取网页信息
            title = await self.page.title()
            url_current = self.page.url
            
            # 获取页面文本内容（前1000个字符）
            content = await self.page.evaluate("() => document.body.innerText")
            content_preview = content[:1000] + "..." if len(content) > 1000 else content
            
            info = f"""
网页标题: {title}
当前URL: {url_current}
页面内容预览:
{content_preview}
            """
            
            self.message_signal.emit(info)
            
            # 开始执行自动化操作
            await self.perform_automation()
            
        except Exception as e:
            self.error_signal.emit(f"打开浏览器失败: {str(e)}")
    
    async def perform_automation(self):
        """执行自动化操作"""
        try:
            self.message_signal.emit("开始执行自动化操作...")
            
            # 等待页面完全加载
            await asyncio.sleep(2)
            
            # 1. 点击"新建"按钮
            self.message_signal.emit("正在查找'新建'按钮...")
            
            # 尝试多种可能的选择器
            new_button_selectors = [
                "button:has-text('新建')",
                "[title*='新建']",
                "button[title*='新建']",
                ".ant-btn:has-text('新建')",
                "button:contains('新建')",
                "//button[contains(text(), '新建')]",
                "//button[@title='新建']",
                "//span[contains(text(), '新建')]/parent::button",
                "//div[contains(text(), '新建')]",
                "[data-testid*='new']",
                "[aria-label*='新建']"
            ]
            
            new_button_found = False
            for selector in new_button_selectors:
                try:
                    if selector.startswith("//"):
                        # XPath选择器
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        # CSS选择器
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)
                    
                    self.message_signal.emit(f"✅ 成功点击'新建'按钮 (选择器: {selector})")
                    new_button_found = True
                    break
                except Exception:
                    continue
            
            if not new_button_found:
                # 如果找不到按钮，尝试获取页面上所有可点击元素
                self.message_signal.emit("未找到'新建'按钮，正在分析页面元素...")
                
                # 获取所有按钮和可点击元素的信息
                elements_info = await self.page.evaluate("""
                    () => {
                        const buttons = document.querySelectorAll('button, [role="button"], .btn, [onclick]');
                        const info = [];
                        buttons.forEach((btn, index) => {
                            const text = btn.innerText || btn.textContent || btn.title || btn.getAttribute('aria-label') || '';
                            const classes = btn.className || '';
                            const id = btn.id || '';
                            if (text.trim() || classes || id) {
                                info.push(`${index}: "${text.trim()}" (class: ${classes}, id: ${id})`);
                            }
                        });
                        return info.slice(0, 20); // 只返回前20个
                    }
                """)
                
                self.message_signal.emit("页面上的按钮元素:")
                for element_info in elements_info:
                    self.message_signal.emit(f"  {element_info}")
                
                # 尝试通过文本内容查找
                try:
                    await self.page.click("text=新建")
                    self.message_signal.emit("✅ 通过文本内容找到并点击了'新建'按钮")
                    new_button_found = True
                except Exception:
                    self.error_signal.emit("❌ 无法找到'新建'按钮，请检查页面是否正确加载")
                    return
            
            # 等待菜单出现
            await asyncio.sleep(2)
            
            # 2. 点击"新建空白页"
            self.message_signal.emit("正在查找'新建空白页'选项...")
            
            blank_page_selectors = [
                "text=新建空白页",
                "button:has-text('新建空白页')",
                "[title*='新建空白页']",
                "//button[contains(text(), '新建空白页')]",
                "//div[contains(text(), '新建空白页')]",
                "//span[contains(text(), '新建空白页')]",
                ".menu-item:has-text('新建空白页')",
                "[data-testid*='blank']"
            ]
            
            blank_page_found = False
            for selector in blank_page_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)
                    
                    self.message_signal.emit(f"✅ 成功点击'新建空白页' (选择器: {selector})")
                    blank_page_found = True
                    break
                except Exception:
                    continue
            
            if not blank_page_found:
                self.error_signal.emit("❌ 无法找到'新建空白页'选项")
                return
            
            # 等待新页面加载
            await asyncio.sleep(3)
            
            # 3. 输入标题
            self.message_signal.emit("正在输入标题...")
            
            title_selectors = [
                "input[placeholder*='标题']",
                "input[placeholder*='请输入标题']",
                "[data-testid*='title']",
                ".title-input",
                "input[name*='title']",
                "//input[contains(@placeholder, '标题')]"
            ]
            
            title_input_found = False
            for selector in title_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.fill(f"xpath={selector}", "02浏览器操作测试网页新建")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.fill(selector, "02浏览器操作测试网页新建")
                    
                    self.message_signal.emit(f"✅ 成功输入标题 (选择器: {selector})")
                    title_input_found = True
                    break
                except Exception:
                    continue
            
            if not title_input_found:
                self.error_signal.emit("❌ 无法找到标题输入框")
                return
            
            # 4. 输入正文
            self.message_signal.emit("正在输入正文...")
            
            content_selectors = [
                "textarea[placeholder*='正文']",
                "textarea[placeholder*='请输入正文']",
                "[data-testid*='content']",
                ".content-input",
                "textarea[name*='content']",
                "//textarea[contains(@placeholder, '正文')]",
                ".ql-editor",  # Quill编辑器
                "[contenteditable='true']"  # 富文本编辑器
            ]
            
            content_input_found = False
            for selector in content_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        if "contenteditable" in selector:
                            await self.page.click(f"xpath={selector}")
                            await self.page.keyboard.type("测试文字：hello！")
                        else:
                            await self.page.fill(f"xpath={selector}", "测试文字：hello！")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        if "contenteditable" in selector:
                            await self.page.click(selector)
                            await self.page.keyboard.type("测试文字：hello！")
                        else:
                            await self.page.fill(selector, "测试文字：hello！")
                    
                    self.message_signal.emit(f"✅ 成功输入正文 (选择器: {selector})")
                    content_input_found = True
                    break
                except Exception:
                    continue
            
            if not content_input_found:
                self.error_signal.emit("❌ 无法找到正文输入框")
                return
            
            # 5. 点击保存
            self.message_signal.emit("正在查找保存按钮...")
            
            save_selectors = [
                "button:has-text('保存')",
                "text=保存",
                "[title*='保存']",
                "button[title*='保存']",
                "//button[contains(text(), '保存')]",
                "//button[@title='保存']",
                "[data-testid*='save']",
                ".save-btn",
                "button[type='submit']"
            ]
            
            save_found = False
            for selector in save_selectors:
                try:
                    if selector.startswith("//"):
                        await self.page.wait_for_selector(f"xpath={selector}", timeout=3000)
                        await self.page.click(f"xpath={selector}")
                    else:
                        await self.page.wait_for_selector(selector, timeout=3000)
                        await self.page.click(selector)
                    
                    self.message_signal.emit(f"✅ 成功点击保存按钮 (选择器: {selector})")
                    save_found = True
                    break
                except Exception:
                    continue
            
            if not save_found:
                self.error_signal.emit("❌ 无法找到保存按钮")
                return
            
            # 等待保存完成
            await asyncio.sleep(2)
            
            self.message_signal.emit("🎉 自动化操作完成！")
            
        except Exception as e:
            self.error_signal.emit(f"自动化操作失败: {str(e)}")
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.message_signal.emit("浏览器已关闭")
            
        except Exception as e:
            self.error_signal.emit(f"关闭浏览器失败: {str(e)}")


class BrowserThread(QThread):
    """浏览器操作线程"""
    def __init__(self, worker, operation, *args):
        super().__init__()
        self.worker = worker
        self.operation = operation
        self.args = args
    
    def run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            if self.operation == "open":
                loop.run_until_complete(self.worker.open_browser_and_operate(*self.args))
            elif self.operation == "close":
                loop.run_until_complete(self.worker.close_browser())
        finally:
            loop.close()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.worker = BrowserWorker()
        self.current_thread = None
        
        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("简化浏览器自动化工具")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 等待时间设置
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("等待时间(秒):"))
        self.wait_time_spinbox = QSpinBox()
        self.wait_time_spinbox.setRange(1, 60)
        self.wait_time_spinbox.setValue(5)
        time_layout.addWidget(self.wait_time_spinbox)
        time_layout.addStretch()
        layout.addLayout(time_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.open_btn = QPushButton("打开浏览器")
        self.close_btn = QPushButton("关闭浏览器")
        
        button_layout.addWidget(self.open_btn)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
        
        # 信息显示区域
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        layout.addWidget(self.info_text)
        
        # 连接按钮事件
        self.open_btn.clicked.connect(self.open_browser)
        self.close_btn.clicked.connect(self.close_browser)
    
    def connect_signals(self):
        """连接信号"""
        self.worker.message_signal.connect(self.show_message)
        self.worker.error_signal.connect(self.show_error)
    
    def show_message(self, message):
        """显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] {message}")
    
    def show_error(self, error):
        """显示错误"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] 错误: {error}")
        QMessageBox.warning(self, "错误", error)
    
    def open_browser(self):
        """打开浏览器并执行自动化操作"""
        url = "https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view"
        wait_seconds = self.wait_time_spinbox.value()
        
        # 禁用按钮防止重复点击
        self.open_btn.setEnabled(False)
        self.show_message("正在启动浏览器，请稍候...")
        
        self.current_thread = BrowserThread(self.worker, "open", url, wait_seconds)
        self.current_thread.finished.connect(lambda: self.open_btn.setEnabled(True))
        self.current_thread.start()
    
    def close_browser(self):
        """关闭浏览器"""
        self.current_thread = BrowserThread(self.worker, "close")
        self.current_thread.start()


def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
