# 示例记录文件 - 展示记录格式
# 实际使用时会自动生成带时间戳的文件名

- action: start_recording
  timestamp: '2024-01-01T10:00:00.000000'
  title: Example Domain
  # url: https://www.example.com
  url: https://mds.zte.com.cn:29001/mds-portal/framework/default.html#/home

- action: click
  data:
    selector: '#example-link'
    target: A
    text: More information...
    x: 400
    y: 300
  timestamp: '2024-01-01T10:00:05.123456'

- action: navigation
  timestamp: '2024-01-01T10:00:06.234567'
  title: IANA — IANA-managed Reserved Domains
  url: https://www.iana.org/domains/example

- action: click
  data:
    selector: '.back-button'
    target: BUTTON
    text: 返回
    x: 100
    y: 50
  timestamp: '2024-01-01T10:00:10.345678'
