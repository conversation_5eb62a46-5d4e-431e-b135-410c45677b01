# 浏览器自动化工具配置文件

# 浏览器设置
browser:
  # Chromium浏览器可执行文件路径
  chromium_path: "C:\\Users\\<USER>\\AppData\\Local\\ms-playwright\\chromium-1155\\chrome-win\\chrome.exe"
  
  # 是否以无头模式运行（true=后台运行，false=显示界面）
  headless: false
  
  # 浏览器启动参数
  args:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"

# 默认设置
defaults:
  # 默认访问的网址
  url: "https://mds.zte.com.cn:29001/mds-portal/framework/default.html#/home"
  
  # 默认等待时间（秒）
  wait_time: 5
  
  # 页面加载超时时间（毫秒）
  timeout: 30000

# 记录设置
recording:
  # 记录文件保存目录
  save_directory: "configs"
  
  # 记录文件名前缀
  file_prefix: "recorded_actions"
  
  # 是否记录鼠标移动
  record_mouse_move: false
  
  # 是否记录键盘输入
  record_keyboard: true
  
  # 点击事件延迟（毫秒）
  click_delay: 100

# 重放设置
replay:
  # 动作之间的延迟（秒）
  action_delay: 1.0
  
  # 是否在重放时显示进度
  show_progress: true
  
  # 重放失败时是否继续
  continue_on_error: true

# 界面设置
ui:
  # 窗口标题
  window_title: "浏览器自动化工具"
  
  # 窗口大小
  window_size:
    width: 800
    height: 600
  
  # 窗口位置
  window_position:
    x: 100
    y: 100

# 日志设置
logging:
  # 是否启用日志
  enabled: true
  
  # 日志级别 (DEBUG, INFO, WARNING, ERROR)
  level: "INFO"
  
  # 日志文件路径
  file_path: "logs/browser_automation.log"
