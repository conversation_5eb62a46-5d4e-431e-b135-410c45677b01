# 简化浏览器自动化工具

这是一个专门为您的需求定制的简化版浏览器自动化工具。

## 功能特性

1. **PySide6界面** - 包含2个按键：打开浏览器、关闭浏览器
2. **多线程操作** - 确保界面不卡顿
3. **指定Chromium浏览器** - 使用您提供的本地路径
4. **自动化操作序列** - 自动执行完整的操作流程
5. **可配置等待时间** - 默认5秒，可调整

## 自动化操作流程

点击"打开浏览器"后，程序将自动执行以下操作：

1. 打开指定的ZTE网址
2. 等待页面加载（可配置时间）
3. 读取并显示页面信息
4. 自动点击"新建"按钮
5. 自动点击"新建空白页"
6. 在标题框输入："02浏览器操作测试网页新建"
7. 在正文框输入："测试文字：hello！"
8. 自动点击保存按钮

## 安装和运行

### 方法1：使用批处理文件
```bash
run_simple.bat
```

### 方法2：手动运行
```bash
# 安装依赖
pip install -r simple_requirements.txt

# 运行程序
python simple_browser_automation.py
```

## 使用说明

1. **启动程序** - 运行脚本后会出现界面
2. **设置等待时间** - 根据网络情况调整等待时间（默认5秒）
3. **点击"打开浏览器"** - 程序会自动执行所有操作
4. **观察日志** - 界面下方会显示详细的操作日志
5. **点击"关闭浏览器"** - 完成后关闭浏览器

## 智能元素查找

程序使用多种策略查找页面元素：

- **文本匹配** - 通过按钮文字查找
- **属性匹配** - 通过title、placeholder等属性查找
- **CSS选择器** - 通过类名、ID等查找
- **XPath选择器** - 通过路径查找
- **备用策略** - 如果主要方法失败，会尝试其他方法

## 错误处理

- 如果找不到某个元素，程序会显示页面上所有可点击元素的信息
- 每个操作步骤都有详细的日志输出
- 操作失败时会显示具体的错误信息

## 文件结构

```
browser/
├── simple_browser_automation.py  # 主程序
├── simple_requirements.txt       # 依赖列表
├── run_simple.bat               # 启动脚本
└── SIMPLE_README.md             # 使用说明
```

## 技术特点

- **异步操作** - 使用async/await确保流畅执行
- **多重选择器** - 提高元素查找成功率
- **详细日志** - 便于调试和监控
- **错误恢复** - 智能处理各种异常情况

## 注意事项

1. 确保Chromium浏览器路径正确
2. 网络连接稳定
3. 目标网站页面结构未发生重大变化
4. 如果某个步骤失败，检查日志中的详细信息

## 自定义修改

如需修改操作流程，可以编辑 `simple_browser_automation.py` 中的 `perform_automation` 方法。

程序设计为高度可配置，可以轻松适应页面结构的变化。
