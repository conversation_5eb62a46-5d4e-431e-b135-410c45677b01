#!/usr/bin/env python3
"""
测试修复后的浏览器自动化工具
验证错误处理和状态检查是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from browser_automation import BrowserWorker, ConfigManager


async def test_browser_worker():
    """测试BrowserWorker的错误处理"""
    print("测试BrowserWorker错误处理...")
    
    config = ConfigManager()
    worker = BrowserWorker(config)
    
    # 测试1: 在没有打开浏览器的情况下尝试记录
    print("\n测试1: 未打开浏览器时记录操作")
    try:
        await worker.start_recording()
        print("❌ 应该抛出错误但没有")
    except Exception as e:
        print(f"✅ 正确捕获错误: {e}")
    
    # 测试2: 在没有打开浏览器的情况下尝试重放
    print("\n测试2: 未打开浏览器时重放操作")
    try:
        await worker.replay_actions("test.yaml")
        print("❌ 应该抛出错误但没有")
    except Exception as e:
        print(f"✅ 正确捕获错误: {e}")
    
    # 测试3: 停止未开始的记录
    print("\n测试3: 停止未开始的记录")
    try:
        await worker.stop_recording_and_save()
        print("❌ 应该抛出错误但没有")
    except Exception as e:
        print(f"✅ 正确捕获错误: {e}")
    
    print("\n✅ 所有错误处理测试通过!")


def test_config_manager():
    """测试配置管理器"""
    print("\n测试配置管理器...")
    
    config = ConfigManager()
    
    # 测试配置读取
    chromium_path = config.get('browser.chromium_path')
    print(f"Chromium路径: {chromium_path}")
    
    default_url = config.get('defaults.url')
    print(f"默认URL: {default_url}")
    
    save_dir = config.get('recording.save_directory', 'configs')
    print(f"保存目录: {save_dir}")
    
    # 测试不存在的配置项
    non_existent = config.get('non.existent.key', 'default_value')
    print(f"不存在的配置项: {non_existent}")
    
    print("✅ 配置管理器测试通过!")


def test_file_structure():
    """测试文件结构"""
    print("\n测试文件结构...")
    
    required_files = [
        'browser_automation.py',
        'config.yaml',
        'requirements.txt',
        'README.md',
        'INSTALL.md'
    ]
    
    required_dirs = [
        'configs'
    ]
    
    current_dir = Path('.')
    
    for file in required_files:
        if (current_dir / file).exists():
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
    
    for dir_name in required_dirs:
        if (current_dir / dir_name).exists():
            print(f"✅ {dir_name}/ 目录存在")
        else:
            print(f"❌ {dir_name}/ 目录不存在")
    
    print("✅ 文件结构检查完成!")


async def main():
    """主测试函数"""
    print("浏览器自动化工具 - 修复验证测试")
    print("=" * 50)
    
    # 测试配置管理器
    test_config_manager()
    
    # 测试文件结构
    test_file_structure()
    
    # 测试BrowserWorker错误处理
    await test_browser_worker()
    
    print("\n" + "=" * 50)
    print("所有测试完成!")
    print("\n使用说明:")
    print("1. 运行主程序: python browser_automation.py")
    print("2. 先点击'打开浏览器'")
    print("3. 然后才能使用'记录人工操作'和'模仿人工操作'")
    print("4. 如果没有先打开浏览器，程序会显示错误提示")


if __name__ == "__main__":
    asyncio.run(main())
