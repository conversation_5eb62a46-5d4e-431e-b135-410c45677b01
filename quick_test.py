#!/usr/bin/env python3
"""
快速测试改进的元素查找功能
"""

def test_element_finding_logic():
    """测试元素查找逻辑"""
    print("测试元素查找逻辑...")
    
    # 模拟页面元素数据
    mock_elements = [
        {
            'text': '新建',
            'tagName': 'BUTTON',
            'className': 'el-button el-button--primary',
            'id': 'new-btn',
            'isVisible': True,
            'isClickable': True
        },
        {
            'text': '新建文档',
            'tagName': 'SPAN',
            'className': 'menu-item',
            'id': '',
            'isVisible': True,
            'isClickable': True
        },
        {
            'text': '新建空白页',
            'tagName': 'DIV',
            'className': 'dropdown-item',
            'id': 'blank-page',
            'isVisible': True,
            'isClickable': True
        }
    ]
    
    print("模拟找到的元素:")
    for i, element in enumerate(mock_elements):
        status = "✅可见" if element['isVisible'] else "❌隐藏"
        clickable = "🖱️可点击" if element['isClickable'] else "🚫不可点击"
        print(f"  [{i}] {element['tagName']}: '{element['text']}' {status} {clickable}")
        if element['className']:
            print(f"      class: {element['className']}")
        if element['id']:
            print(f"      id: {element['id']}")
    
    # 测试选择器生成
    print("\n生成的选择器:")
    for element in mock_elements:
        selectors = []
        
        if element['id']:
            selectors.append(f"#{element['id']}")
        
        if element['className']:
            first_class = element['className'].split()[0]
            selectors.append(f".{first_class}")
        
        selectors.append(f"text={element['text']}")
        selectors.append(f"{element['tagName'].lower()}:has-text('{element['text']}')")
        
        print(f"  '{element['text']}' -> {selectors}")
    
    print("\n✅ 元素查找逻辑测试完成")


def test_selector_strategies():
    """测试选择器策略"""
    print("\n测试选择器策略...")
    
    # 各种可能的选择器
    selectors = [
        "text=新建",
        "button:has-text('新建')",
        "[title*='新建']",
        "[aria-label*='新建']",
        "//button[contains(text(), '新建')]",
        "//span[contains(text(), '新建')]",
        "//div[contains(text(), '新建')]",
        "//*[contains(text(), '新建')]",
        ".el-button:has-text('新建')",
        ".ant-btn:has-text('新建')",
        "[data-testid*='new']",
        "[data-testid*='create']"
    ]
    
    print("将尝试的选择器策略:")
    for i, selector in enumerate(selectors):
        selector_type = "XPath" if selector.startswith("//") or selector.startswith("//*") else "CSS"
        print(f"  [{i+1:2d}] {selector_type:5s}: {selector}")
    
    print(f"\n总共 {len(selectors)} 种选择器策略")
    print("✅ 选择器策略测试完成")


def main():
    """主函数"""
    print("改进版浏览器自动化工具 - 快速测试")
    print("=" * 50)
    
    test_element_finding_logic()
    test_selector_strategies()
    
    print("\n" + "=" * 50)
    print("测试总结:")
    print("1. ✅ 元素查找逻辑已优化")
    print("2. ✅ 多重选择器策略已准备")
    print("3. ✅ 详细日志输出已实现")
    print("4. ✅ 智能元素分析已完成")
    print("\n建议:")
    print("- 运行 simple_browser_automation.py 测试实际效果")
    print("- 观察详细的页面分析日志")
    print("- 检查是否能找到'新建'按钮")


if __name__ == "__main__":
    main()
