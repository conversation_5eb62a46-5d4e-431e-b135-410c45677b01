#!/usr/bin/env python3
"""
浏览器自动化工具测试示例
这个脚本演示如何使用浏览器自动化工具的各种功能
"""

import os
import sys
import time
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import PySide6
        import playwright
        import yaml
        print("✓ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_chromium_path():
    """检查Chromium路径是否存在"""
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    
    if os.path.exists(chromium_path):
        print(f"✓ Chromium浏览器路径存在: {chromium_path}")
        return True
    else:
        print(f"✗ Chromium浏览器路径不存在: {chromium_path}")
        print("请检查路径或安装Playwright浏览器:")
        print("playwright install chromium")
        return False

def check_configs_folder():
    """检查configs文件夹"""
    configs_dir = Path("configs")
    if configs_dir.exists():
        print(f"✓ configs文件夹存在")
        
        # 列出现有的记录文件
        yaml_files = list(configs_dir.glob("*.yaml"))
        if yaml_files:
            print(f"  发现 {len(yaml_files)} 个记录文件:")
            for file in yaml_files:
                print(f"    - {file.name}")
        else:
            print("  暂无记录文件")
        return True
    else:
        print("✗ configs文件夹不存在，将创建...")
        configs_dir.mkdir(exist_ok=True)
        print("✓ configs文件夹已创建")
        return True

def main():
    """主函数"""
    print("浏览器自动化工具 - 环境检查")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    print()
    
    # 检查Chromium路径
    if not check_chromium_path():
        return False
    
    print()
    
    # 检查configs文件夹
    check_configs_folder()
    
    print()
    print("=" * 40)
    print("环境检查完成！")
    print()
    print("使用说明:")
    print("1. 运行主程序: python browser_automation.py")
    print("2. 或使用批处理文件: run.bat")
    print()
    print("操作流程:")
    print("1. 点击'打开浏览器'")
    print("2. 点击'记录人工操作'开始记录")
    print("3. 在浏览器中进行操作")
    print("4. 再次点击'记录人工操作'停止记录")
    print("5. 点击'模仿人工操作'重放操作")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
