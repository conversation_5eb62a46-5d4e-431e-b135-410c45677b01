#!/usr/bin/env python3
"""
简化浏览器自动化工具测试脚本
验证程序的基本功能
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import PySide6
        import playwright
        print("✅ 所有依赖已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r simple_requirements.txt")
        return False

def check_chromium_path():
    """检查Chromium路径是否存在"""
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    
    if os.path.exists(chromium_path):
        print(f"✅ Chromium浏览器路径存在: {chromium_path}")
        return True
    else:
        print(f"❌ Chromium浏览器路径不存在: {chromium_path}")
        print("请检查路径或安装Playwright浏览器:")
        print("playwright install chromium")
        return False

def check_file_structure():
    """检查文件结构"""
    print("检查文件结构...")
    
    required_files = [
        'simple_browser_automation.py',
        'simple_requirements.txt',
        'run_simple.bat',
        'SIMPLE_README.md'
    ]
    
    current_dir = Path('.')
    
    for file in required_files:
        if (current_dir / file).exists():
            print(f"✅ {file} 存在")
        else:
            print(f"❌ {file} 不存在")
    
    print("✅ 文件结构检查完成!")

def test_import():
    """测试导入主模块"""
    try:
        sys.path.insert(0, str(Path(__file__).parent))
        from simple_browser_automation import BrowserWorker, MainWindow
        print("✅ 主模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 主模块导入失败: {e}")
        return False

def main():
    """主测试函数"""
    print("简化浏览器自动化工具 - 环境检查")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    print()
    
    # 检查Chromium路径
    if not check_chromium_path():
        return False
    
    print()
    
    # 检查文件结构
    check_file_structure()
    
    print()
    
    # 测试模块导入
    if not test_import():
        return False
    
    print()
    print("=" * 50)
    print("✅ 环境检查完成！")
    print()
    print("使用说明:")
    print("1. 运行主程序: python simple_browser_automation.py")
    print("2. 或使用批处理文件: run_simple.bat")
    print()
    print("自动化操作流程:")
    print("1. 点击'打开浏览器'")
    print("2. 程序自动访问ZTE网址")
    print("3. 自动执行完整的操作序列:")
    print("   - 点击'新建'")
    print("   - 点击'新建空白页'")
    print("   - 输入标题: '02浏览器操作测试网页新建'")
    print("   - 输入正文: '测试文字：hello！'")
    print("   - 点击保存")
    print("4. 完成后可点击'关闭浏览器'")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
