import sys
import os
import yaml
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any

from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                               QHBoxLayout, QWidget, QPushButton, QTextEdit,
                               QSpinBox, QLabel, QMessageBox, QLineEdit)
from PySide6.QtCore import QThread, Signal, QObject
from playwright.async_api import async_playwright


class ConfigManager:
    """配置管理器"""
    def __init__(self, config_file="config.yaml"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return yaml.safe_load(f)
            else:
                return self.get_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            return self.get_default_config()

    def get_default_config(self):
        """获取默认配置"""
        return {
            'browser': {
                'chromium_path': r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe",
                'headless': False
            },
            'defaults': {
                # 'url': 'https://www.example.com',
                'url':  "https://mds.zte.com.cn:29001/mds-portal/framework/default.html#/home",
                'wait_time': 5
            },
            'recording': {
                'save_directory': 'configs',
                'file_prefix': 'recorded_actions'
            },
            'replay': {
                'action_delay': 1.0
            }
        }

    def get(self, key_path, default=None):
        """获取配置值，支持点号分隔的路径"""
        keys = key_path.split('.')
        value = self.config

        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return default

        return value


class BrowserWorker(QObject):
    """浏览器操作的工作线程"""
    message_signal = Signal(str)
    error_signal = Signal(str)

    def __init__(self, config_manager):
        super().__init__()
        self.config = config_manager
        self.browser = None
        self.page = None
        self.playwright = None
        self.is_recording = False
        self.recorded_actions = []
        
    async def open_browser(self, url: str, wait_seconds: int):
        """打开浏览器并访问网址"""
        try:
            self.message_signal.emit("正在启动浏览器...")

            # 从配置获取Chromium路径
            chromium_path = self.config.get('browser.chromium_path')
            headless = self.config.get('browser.headless', False)

            self.playwright = await async_playwright().start()
            self.browser = await self.playwright.chromium.launch(
                executable_path=chromium_path,
                headless=headless
            )
            
            self.page = await self.browser.new_page()
            
            self.message_signal.emit(f"正在访问网址: {url}")
            await self.page.goto(url)
            
            self.message_signal.emit(f"等待 {wait_seconds} 秒...")
            await asyncio.sleep(wait_seconds)
            
            # 读取网页信息
            title = await self.page.title()
            url_current = self.page.url
            
            # 获取页面文本内容（前1000个字符）
            content = await self.page.evaluate("() => document.body.innerText")
            content_preview = content[:1000] + "..." if len(content) > 1000 else content
            
            info = f"""
网页标题: {title}
当前URL: {url_current}
页面内容预览:
{content_preview}
            """
            
            self.message_signal.emit(info)
            
        except Exception as e:
            self.error_signal.emit(f"打开浏览器失败: {str(e)}")
    
    async def close_browser(self):
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
                self.page = None
            if self.browser:
                await self.browser.close()
                self.browser = None
            if self.playwright:
                await self.playwright.stop()
                self.playwright = None
            
            self.message_signal.emit("浏览器已关闭")
            
        except Exception as e:
            self.error_signal.emit(f"关闭浏览器失败: {str(e)}")
    
    async def start_recording(self):
        """开始记录操作"""
        self.message_signal.emit("正在检查浏览器状态...")

        if not self.browser:
            self.error_signal.emit("浏览器未启动，请先点击'打开浏览器'")
            return

        if not self.page:
            self.error_signal.emit("页面未加载，请先点击'打开浏览器'")
            return

        try:
            # 检查页面是否仍然有效
            self.message_signal.emit("检查页面状态...")
            if self.page.is_closed():
                self.error_signal.emit("浏览器页面已关闭，请重新打开浏览器")
                return

            # 重置记录状态
            self.is_recording = True
            self.recorded_actions = []
            self.message_signal.emit("✅ 浏览器状态正常，开始记录操作...")

            # 记录初始状态
            try:
                self.message_signal.emit("获取页面信息...")
                current_url = self.page.url
                page_title = await self.page.title()
                self.message_signal.emit(f"当前页面: {page_title}")
                self.message_signal.emit(f"当前URL: {current_url}")
            except Exception as e:
                self.is_recording = False
                self.error_signal.emit(f"无法获取页面信息: {str(e)}")
                return

            initial_state = {
                'timestamp': datetime.now().isoformat(),
                'action': 'start_recording',
                'url': current_url,
                'title': page_title
            }
            self.recorded_actions.append(initial_state)

            # 设置事件监听器
            self.message_signal.emit("设置事件监听器...")
            await self._setup_event_listeners()

            self.message_signal.emit("🎯 记录已开始！请在浏览器中进行操作...")

        except Exception as e:
            self.is_recording = False
            self.error_signal.emit(f"开始记录失败: {str(e)}")
            import traceback
            self.message_signal.emit(f"详细错误信息: {traceback.format_exc()}")
    
    async def _setup_event_listeners(self):
        """设置事件监听器"""
        try:
            # 监听页面导航
            self.page.on("framenavigated", self._on_navigation)
            self.message_signal.emit("✅ 页面导航监听器已设置")

            # 清除之前的点击记录
            await self.page.evaluate("() => { window.recordedClicks = []; }")

            # 监听点击事件
            await self.page.evaluate("""
                () => {
                    // 清除之前的监听器
                    if (window.clickListener) {
                        document.removeEventListener('click', window.clickListener);
                    }

                    // 创建新的点击监听器
                    window.clickListener = (event) => {
                        const clickData = {
                            timestamp: new Date().toISOString(),
                            x: event.clientX,
                            y: event.clientY,
                            target: event.target.tagName,
                            text: (event.target.innerText || event.target.value || '').substring(0, 100),
                            selector: event.target.id ? '#' + event.target.id :
                                     event.target.className ? '.' + event.target.className.split(' ')[0] :
                                     event.target.tagName.toLowerCase(),
                            href: event.target.href || '',
                            type: event.target.type || ''
                        };

                        // 存储到数组中
                        if (!window.recordedClicks) {
                            window.recordedClicks = [];
                        }
                        window.recordedClicks.push(clickData);

                        // 也保存最新的点击到单独变量
                        window.recordedClick = clickData;

                        console.log('记录点击:', clickData);
                    };

                    // 添加事件监听器
                    document.addEventListener('click', window.clickListener, true);
                    console.log('点击事件监听器已设置');
                }
            """)
            self.message_signal.emit("✅ 点击事件监听器已设置")

        except Exception as e:
            self.error_signal.emit(f"设置事件监听器失败: {str(e)}")
            raise
    
    async def _on_navigation(self, frame):
        """页面导航事件处理"""
        if self.is_recording and frame == self.page.main_frame:
            action = {
                'timestamp': datetime.now().isoformat(),
                'action': 'navigation',
                'url': frame.url,
                'title': await self.page.title()
            }
            self.recorded_actions.append(action)
            self.message_signal.emit(f"记录导航: {frame.url}")
    
    async def stop_recording_and_save(self):
        """停止记录并保存"""
        if not self.is_recording:
            self.error_signal.emit("当前没有在记录")
            return

        try:
            self.message_signal.emit("正在停止记录...")
            self.is_recording = False

            # 检查页面是否仍然有效
            if not self.page or self.page.is_closed():
                self.message_signal.emit("⚠️ 浏览器页面已关闭，但将保存已记录的操作")
            else:
                # 获取所有记录的点击事件
                try:
                    self.message_signal.emit("收集记录的点击事件...")
                    click_events = await self.page.evaluate("() => window.recordedClicks || []")

                    if click_events:
                        self.message_signal.emit(f"发现 {len(click_events)} 个点击事件")
                        for click_data in click_events:
                            action = {
                                'timestamp': click_data.get('timestamp', datetime.now().isoformat()),
                                'action': 'click',
                                'data': click_data
                            }
                            self.recorded_actions.append(action)
                    else:
                        self.message_signal.emit("没有检测到点击事件")

                    # 清理事件监听器
                    await self.page.evaluate("""
                        () => {
                            if (window.clickListener) {
                                document.removeEventListener('click', window.clickListener);
                                window.clickListener = null;
                            }
                            window.recordedClicks = [];
                            window.recordedClick = null;
                        }
                    """)

                except Exception as e:
                    self.message_signal.emit(f"获取点击事件失败: {str(e)}")

            # 保存到YAML文件
            save_dir = self.config.get('recording.save_directory', 'configs')
            file_prefix = self.config.get('recording.file_prefix', 'recorded_actions')

            configs_dir = Path(save_dir)
            configs_dir.mkdir(exist_ok=True)

            filename = f"{file_prefix}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.yaml"
            filepath = configs_dir / filename

            self.message_signal.emit(f"保存记录到: {filename}")

            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(self.recorded_actions, f, default_flow_style=False,
                         allow_unicode=True, indent=2)

            self.message_signal.emit(f"✅ 操作记录已保存到: {filepath}")
            self.message_signal.emit(f"📊 共记录了 {len(self.recorded_actions)} 个操作")

            # 显示记录摘要
            action_types = {}
            for action in self.recorded_actions:
                action_type = action['action']
                action_types[action_type] = action_types.get(action_type, 0) + 1

            summary = ", ".join([f"{k}: {v}" for k, v in action_types.items()])
            self.message_signal.emit(f"📋 操作类型统计: {summary}")

        except Exception as e:
            self.error_signal.emit(f"保存记录失败: {str(e)}")
            import traceback
            self.message_signal.emit(f"详细错误信息: {traceback.format_exc()}")
    
    async def replay_actions(self, yaml_file: str):
        """重放操作"""
        if not self.browser or not self.page:
            self.error_signal.emit("请先打开浏览器")
            return

        try:
            # 检查页面是否仍然有效
            if self.page.is_closed():
                self.error_signal.emit("浏览器页面已关闭，请重新打开浏览器")
                return

            if not os.path.exists(yaml_file):
                self.error_signal.emit(f"文件不存在: {yaml_file}")
                return

            with open(yaml_file, 'r', encoding='utf-8') as f:
                actions = yaml.safe_load(f)

            if not actions:
                self.error_signal.emit("记录文件为空")
                return

            self.message_signal.emit(f"开始重放操作，共 {len(actions)} 个动作")

            for i, action in enumerate(actions):
                self.message_signal.emit(f"执行动作 {i+1}/{len(actions)}: {action['action']}")

                if action['action'] == 'start_recording':
                    # 导航到初始URL
                    try:
                        await self.page.goto(action['url'])
                        await asyncio.sleep(2)
                    except Exception as e:
                        self.message_signal.emit(f"导航失败: {str(e)}")

                elif action['action'] == 'navigation':
                    try:
                        await self.page.goto(action['url'])
                        await asyncio.sleep(2)
                    except Exception as e:
                        self.message_signal.emit(f"导航失败: {str(e)}")

                elif action['action'] == 'click':
                    if 'data' in action:
                        click_data = action['data']
                        try:
                            # 尝试通过选择器点击
                            await self.page.click(click_data['selector'], timeout=5000)
                            self.message_signal.emit(f"点击成功: {click_data['selector']}")
                        except Exception:
                            try:
                                # 如果选择器失败，尝试坐标点击
                                await self.page.mouse.click(click_data['x'], click_data['y'])
                                self.message_signal.emit(f"坐标点击成功: ({click_data['x']}, {click_data['y']})")
                            except Exception as e:
                                self.message_signal.emit(f"点击失败: {str(e)}")
                        await asyncio.sleep(1)

            self.message_signal.emit("操作重放完成")

        except Exception as e:
            self.error_signal.emit(f"重放操作失败: {str(e)}")


class BrowserThread(QThread):
    """浏览器操作线程"""
    def __init__(self, worker, operation, *args):
        super().__init__()
        self.worker = worker
        self.operation = operation
        self.args = args
    
    def run(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            if self.operation == "open":
                loop.run_until_complete(self.worker.open_browser(*self.args))
            elif self.operation == "close":
                loop.run_until_complete(self.worker.close_browser())
            elif self.operation == "record":
                loop.run_until_complete(self.worker.start_recording())
            elif self.operation == "stop_record":
                loop.run_until_complete(self.worker.stop_recording_and_save())
            elif self.operation == "replay":
                loop.run_until_complete(self.worker.replay_actions(*self.args))
        finally:
            loop.close()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.config = ConfigManager()
        self.worker = BrowserWorker(self.config)
        self.current_thread = None
        self.is_recording = False

        self.init_ui()
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        # 从配置获取窗口设置
        window_title = self.config.get('ui.window_title', '浏览器自动化工具')
        window_size = self.config.get('ui.window_size', {'width': 800, 'height': 600})
        window_pos = self.config.get('ui.window_position', {'x': 100, 'y': 100})

        self.setWindowTitle(window_title)
        self.setGeometry(window_pos['x'], window_pos['y'],
                        window_size['width'], window_size['height'])

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # URL输入设置
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("网址:"))
        self.url_input = QLineEdit()
        # default_url = self.config.get('defaults.url', 'https://www.example.com')
        default_url = self.config.get('defaults.url', "https://mds.zte.com.cn:29001/mds-portal/framework/default.html#/home")
        self.url_input.setText(default_url)
        url_layout.addWidget(self.url_input)
        layout.addLayout(url_layout)

        # 等待时间设置
        time_layout = QHBoxLayout()
        time_layout.addWidget(QLabel("等待时间(秒):"))
        self.wait_time_spinbox = QSpinBox()
        self.wait_time_spinbox.setRange(1, 60)
        default_wait = self.config.get('defaults.wait_time', 5)
        self.wait_time_spinbox.setValue(default_wait)
        time_layout.addWidget(self.wait_time_spinbox)
        time_layout.addStretch()
        layout.addLayout(time_layout)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        self.open_btn = QPushButton("打开浏览器")
        self.close_btn = QPushButton("关闭浏览器")
        self.record_btn = QPushButton("记录人工操作")
        self.replay_btn = QPushButton("模仿人工操作")
        
        button_layout.addWidget(self.open_btn)
        button_layout.addWidget(self.close_btn)
        button_layout.addWidget(self.record_btn)
        button_layout.addWidget(self.replay_btn)
        
        layout.addLayout(button_layout)
        
        # 信息显示区域
        self.info_text = QTextEdit()
        self.info_text.setReadOnly(True)
        layout.addWidget(self.info_text)
        
        # 连接按钮事件
        self.open_btn.clicked.connect(self.open_browser)
        self.close_btn.clicked.connect(self.close_browser)
        self.record_btn.clicked.connect(self.toggle_recording)
        self.replay_btn.clicked.connect(self.replay_actions)
    
    def connect_signals(self):
        """连接信号"""
        self.worker.message_signal.connect(self.show_message)
        self.worker.error_signal.connect(self.show_error)
    
    def show_message(self, message):
        """显示消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] {message}")
    
    def show_error(self, error):
        """显示错误"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.info_text.append(f"[{timestamp}] 错误: {error}")
        QMessageBox.warning(self, "错误", error)
    
    def open_browser(self):
        """打开浏览器"""
        url = self.url_input.text().strip()
        if not url:
            self.show_error("请输入网址")
            return

        wait_seconds = self.wait_time_spinbox.value()

        # 禁用按钮防止重复点击
        self.open_btn.setEnabled(False)
        self.show_message("正在启动浏览器，请稍候...")

        self.current_thread = BrowserThread(
            self.worker, "open", url, wait_seconds)
        self.current_thread.finished.connect(
            lambda: self.open_btn.setEnabled(True))
        self.current_thread.start()

    def close_browser(self):
        """关闭浏览器"""
        self.current_thread = BrowserThread(self.worker, "close")
        self.current_thread.start()

    def toggle_recording(self):
        """切换录制状态"""
        if not self.is_recording:
            # 检查是否有浏览器实例
            if not self.worker.browser or not self.worker.page:
                self.show_error("请先打开浏览器")
                return

            self.current_thread = BrowserThread(self.worker, "record")
            self.current_thread.finished.connect(self.on_recording_started)
            self.current_thread.start()

            # 禁用按钮防止重复点击
            self.record_btn.setEnabled(False)
            self.record_btn.setText("正在启动记录...")
        else:
            self.current_thread = BrowserThread(self.worker, "stop_record")
            self.current_thread.finished.connect(self.on_recording_stopped)
            self.current_thread.start()

            # 禁用按钮防止重复点击
            self.record_btn.setEnabled(False)
            self.record_btn.setText("正在停止记录...")

    def on_recording_started(self):
        """记录开始后的回调"""
        self.record_btn.setEnabled(True)
        if self.worker.is_recording:
            self.record_btn.setText("停止记录")
            self.is_recording = True
        else:
            self.record_btn.setText("记录人工操作")
            self.is_recording = False

    def on_recording_stopped(self):
        """记录停止后的回调"""
        self.record_btn.setEnabled(True)
        self.record_btn.setText("记录人工操作")
        self.is_recording = False

    def replay_actions(self):
        """重放操作"""
        # 检查是否有浏览器实例
        if not self.worker.browser or not self.worker.page:
            self.show_error("请先打开浏览器")
            return

        # 从配置获取保存目录和文件前缀
        save_dir = self.config.get('recording.save_directory', 'configs')
        file_prefix = self.config.get('recording.file_prefix', 'recorded_actions')

        configs_dir = Path(save_dir)
        if not configs_dir.exists():
            self.show_error(f"{save_dir}文件夹不存在")
            return

        yaml_files = list(configs_dir.glob(f"{file_prefix}_*.yaml"))
        if not yaml_files:
            self.show_error("没有找到记录文件")
            return

        # 使用最新的文件
        latest_file = max(yaml_files, key=os.path.getctime)
        self.show_message(f"将重放文件: {latest_file.name}")

        # 禁用按钮防止重复点击
        self.replay_btn.setEnabled(False)
        self.replay_btn.setText("正在重放...")

        self.current_thread = BrowserThread(
            self.worker, "replay", str(latest_file))
        self.current_thread.finished.connect(self.on_replay_finished)
        self.current_thread.start()

    def on_replay_finished(self):
        """重放完成后的回调"""
        self.replay_btn.setEnabled(True)
        self.replay_btn.setText("模仿人工操作")


def main():
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
