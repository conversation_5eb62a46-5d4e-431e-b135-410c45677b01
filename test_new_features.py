#!/usr/bin/env python3
"""
测试新的分步功能
验证分解后的操作流程
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright


async def test_step_by_step_operations():
    """测试分步操作"""
    print("测试分步操作流程...")
    
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    url = "https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view"
    
    playwright = None
    browser = None
    
    try:
        # 步骤1: 打开浏览器
        print("步骤1: 打开浏览器...")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            executable_path=chromium_path,
            headless=False
        )
        
        page = await browser.new_page()
        print("✅ 浏览器已启动")
        
        # 步骤2: 打开网页
        print("步骤2: 打开网页...")
        await page.goto(url, wait_until="networkidle")
        await asyncio.sleep(5)  # 等待页面加载
        
        title = await page.title()
        print(f"✅ 网页已打开: {title}")
        
        # 步骤3: 标记网页元素
        print("步骤3: 标记网页元素...")
        
        result = await page.evaluate("""
            () => {
                // 移除之前的标记
                const existingMarkers = document.querySelectorAll('.element-marker');
                existingMarkers.forEach(marker => marker.remove());
                
                // 添加CSS样式
                const style = document.createElement('style');
                style.textContent = `
                    .element-marker {
                        position: absolute;
                        border: 2px solid red;
                        background-color: rgba(255, 0, 0, 0.1);
                        pointer-events: none;
                        z-index: 10000;
                        box-sizing: border-box;
                    }
                    
                    .element-number {
                        position: absolute;
                        top: -15px;
                        left: -2px;
                        background-color: red;
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                        padding: 2px 6px;
                        border-radius: 3px;
                        min-width: 20px;
                        text-align: center;
                        font-family: Arial, sans-serif;
                    }
                `;
                document.head.appendChild(style);
                
                // 查找需要标记的元素
                const elementsToMark = [];
                
                // 标题元素
                document.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"]').forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'title'});
                    }
                });
                
                // 图片元素
                document.querySelectorAll('img').forEach(el => {
                    if (el.offsetWidth > 30 && el.offsetHeight > 30) {
                        elementsToMark.push({element: el, type: 'image'});
                    }
                });
                
                // 链接元素
                document.querySelectorAll('a').forEach(el => {
                    const text = (el.innerText || '').trim();
                    if (text && el.offsetWidth > 0 && el.offsetHeight > 0 && text.length > 2) {
                        elementsToMark.push({element: el, type: 'link'});
                    }
                });
                
                // 按钮元素
                document.querySelectorAll('button, input[type="button"], [role="button"]').forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'button'});
                    }
                });
                
                // 限制标记数量
                const selectedElements = elementsToMark.slice(0, 50);
                
                // 为每个元素添加标记
                selectedElements.forEach((item, index) => {
                    const element = item.element;
                    const rect = element.getBoundingClientRect();
                    
                    const marker = document.createElement('div');
                    marker.className = 'element-marker';
                    marker.style.left = (rect.left + window.scrollX) + 'px';
                    marker.style.top = (rect.top + window.scrollY) + 'px';
                    marker.style.width = rect.width + 'px';
                    marker.style.height = rect.height + 'px';
                    
                    const number = document.createElement('div');
                    number.className = 'element-number';
                    number.textContent = index + 1;
                    marker.appendChild(number);
                    
                    document.body.appendChild(marker);
                });
                
                return {
                    totalElements: selectedElements.length,
                    types: {
                        title: selectedElements.filter(item => item.type === 'title').length,
                        image: selectedElements.filter(item => item.type === 'image').length,
                        link: selectedElements.filter(item => item.type === 'link').length,
                        button: selectedElements.filter(item => item.type === 'button').length
                    }
                };
            }
        """)
        
        print(f"✅ 元素标记完成，共标记 {result['totalElements']} 个元素")
        print(f"   标题: {result['types']['title']} 个")
        print(f"   图片: {result['types']['image']} 个")
        print(f"   链接: {result['types']['link']} 个")
        print(f"   按钮: {result['types']['button']} 个")
        
        # 步骤4: 显示页面信息
        print("步骤4: 读取页面信息...")
        
        content = await page.evaluate("() => document.body.innerText || ''")
        content_length = len(content.strip())
        
        page_info = f"""
页面标题: {title}
页面URL: {page.url}
页面内容长度: {content_length} 字符
页面内容预览:
{content[:300] + "..." if len(content) > 300 else content}
        """
        
        print(page_info)
        
        # 步骤5: 测试查找"新建"按钮
        print("步骤5: 查找'新建'按钮...")
        
        new_elements = await page.evaluate("""
            () => {
                const elements = [];
                document.querySelectorAll('*').forEach((el, index) => {
                    const text = (el.innerText || el.textContent || '').trim();
                    if (text.includes('新建') || text.includes('新增') || text.includes('创建')) {
                        const rect = el.getBoundingClientRect();
                        elements.push({
                            index: index,
                            text: text.substring(0, 50),
                            tagName: el.tagName,
                            className: el.className || '',
                            id: el.id || '',
                            isVisible: rect.width > 0 && rect.height > 0
                        });
                    }
                });
                
                return elements.filter(el => el.isVisible).slice(0, 10);
            }
        """)
        
        if len(new_elements) > 0:
            print(f"找到 {len(new_elements)} 个包含'新建'的元素:")
            for i, element in enumerate(new_elements):
                print(f"  [{i+1}] {element['tagName']}: '{element['text']}'")
                if element['className']:
                    print(f"      class: {element['className']}")
                if element['id']:
                    print(f"      id: {element['id']}")
        else:
            print("⚠️ 未找到包含'新建'的元素")
        
        print("\n🎉 分步测试完成！")
        print("现在可以使用主程序的各个按钮进行操作:")
        print("1. 打开浏览器 - 只启动浏览器")
        print("2. 打开网页 - 访问指定网址并标记元素")
        print("3. 新建 - 点击网页上的'新建'按钮")
        print("4. 新建空白页 - 点击'新建空白页'")
        print("5. 保存 - 填写表单并保存")
        
        print("\n浏览器将保持打开状态供您测试...")
        print("按 Ctrl+C 退出")
        
        # 保持浏览器打开
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断，关闭浏览器...")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()


def main():
    """主函数"""
    print("新功能分步测试")
    print("=" * 50)
    print("这个测试将验证:")
    print("1. 打开浏览器")
    print("2. 打开指定网页")
    print("3. 标记网页元素")
    print("4. 读取页面信息")
    print("5. 查找'新建'按钮")
    print("=" * 50)
    
    try:
        asyncio.run(test_step_by_step_operations())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
