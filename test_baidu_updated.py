#!/usr/bin/env python3
"""
测试更新后的百度功能
验证新的操作流程：百度首页标记 -> 新闻 -> 今日辟谣
"""

import asyncio
import sys
from pathlib import Path
from playwright.async_api import async_playwright


async def test_updated_baidu_flow():
    """测试更新后的百度操作流程"""
    print("测试更新后的百度操作流程...")
    
    chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
    
    playwright = None
    browser = None
    
    try:
        print("启动浏览器...")
        playwright = await async_playwright().start()
        browser = await playwright.chromium.launch(
            executable_path=chromium_path,
            headless=False
        )
        
        page = await browser.new_page()
        
        # 1. 访问百度
        print("1. 访问百度...")
        await page.goto("https://www.baidu.com", wait_until="networkidle")
        await asyncio.sleep(3)
        
        title = await page.title()
        print(f"✅ 成功打开百度: {title}")
        
        # 2. 给百度首页添加元素标记
        print("2. 给百度首页添加元素标记...")
        
        result = await page.evaluate("""
            () => {
                // 移除之前的标记
                const existingMarkers = document.querySelectorAll('.element-marker');
                existingMarkers.forEach(marker => marker.remove());
                
                // 添加CSS样式
                const style = document.createElement('style');
                style.textContent = `
                    .element-marker {
                        position: absolute;
                        border: 2px solid red;
                        background-color: rgba(255, 0, 0, 0.1);
                        pointer-events: none;
                        z-index: 10000;
                        box-sizing: border-box;
                    }
                    
                    .element-number {
                        position: absolute;
                        top: -15px;
                        left: -2px;
                        background-color: red;
                        color: white;
                        font-size: 12px;
                        font-weight: bold;
                        padding: 2px 6px;
                        border-radius: 3px;
                        min-width: 20px;
                        text-align: center;
                        font-family: Arial, sans-serif;
                    }
                `;
                document.head.appendChild(style);
                
                // 查找需要标记的元素
                const elementsToMark = [];
                
                // 标题元素
                const titles = document.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"]');
                titles.forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'title'});
                    }
                });
                
                // 图片元素
                const images = document.querySelectorAll('img');
                images.forEach(el => {
                    if (el.offsetWidth > 30 && el.offsetHeight > 30) {
                        elementsToMark.push({element: el, type: 'image'});
                    }
                });
                
                // 链接元素
                const links = document.querySelectorAll('a');
                links.forEach(el => {
                    const text = (el.innerText || '').trim();
                    if (text && el.offsetWidth > 0 && el.offsetHeight > 0 && text.length > 1) {
                        elementsToMark.push({element: el, type: 'link'});
                    }
                });
                
                // 输入框
                const inputs = document.querySelectorAll('input, textarea');
                inputs.forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'input'});
                    }
                });
                
                // 按钮元素
                const buttons = document.querySelectorAll('button, input[type="button"], input[type="submit"]');
                buttons.forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'button'});
                    }
                });
                
                // 限制标记数量
                const maxMarkers = 50;
                const selectedElements = elementsToMark.slice(0, maxMarkers);
                
                // 为每个元素添加标记
                selectedElements.forEach((item, index) => {
                    const element = item.element;
                    const rect = element.getBoundingClientRect();
                    
                    // 创建红框标记
                    const marker = document.createElement('div');
                    marker.className = 'element-marker';
                    marker.style.left = (rect.left + window.scrollX) + 'px';
                    marker.style.top = (rect.top + window.scrollY) + 'px';
                    marker.style.width = rect.width + 'px';
                    marker.style.height = rect.height + 'px';
                    
                    // 创建序号标记
                    const number = document.createElement('div');
                    number.className = 'element-number';
                    number.textContent = index + 1;
                    marker.appendChild(number);
                    
                    document.body.appendChild(marker);
                });
                
                return {
                    totalElements: selectedElements.length,
                    types: {
                        title: selectedElements.filter(item => item.type === 'title').length,
                        image: selectedElements.filter(item => item.type === 'image').length,
                        link: selectedElements.filter(item => item.type === 'link').length,
                        input: selectedElements.filter(item => item.type === 'input').length,
                        button: selectedElements.filter(item => item.type === 'button').length
                    }
                };
            }
        """)
        
        print(f"✅ 百度首页标记完成，共标记 {result['totalElements']} 个元素")
        print(f"   标题: {result['types']['title']} 个")
        print(f"   图片: {result['types']['image']} 个")
        print(f"   链接: {result['types']['link']} 个")
        print(f"   输入框: {result['types']['input']} 个")
        print(f"   按钮: {result['types']['button']} 个")
        
        # 等待用户查看标记效果
        print("\n等待3秒查看标记效果...")
        await asyncio.sleep(3)
        
        # 3. 点击"新闻"
        print("3. 查找并点击'新闻'...")
        
        news_selectors = [
            "text=新闻",
            "a:has-text('新闻')",
            "[href*='news']",
            "//a[contains(text(), '新闻')]"
        ]
        
        news_found = False
        for selector in news_selectors:
            try:
                print(f"   尝试选择器: {selector}")
                if selector.startswith("//"):
                    await page.wait_for_selector(f"xpath={selector}", timeout=3000)
                    await page.click(f"xpath={selector}")
                else:
                    await page.wait_for_selector(selector, timeout=3000)
                    await page.click(selector)
                
                print(f"✅ 成功点击'新闻' (选择器: {selector})")
                news_found = True
                break
            except Exception as e:
                print(f"   失败: {e}")
                continue
        
        if not news_found:
            print("❌ 无法找到'新闻'链接")
            return
        
        # 等待新闻页面加载
        await asyncio.sleep(3)
        
        # 4. 查找并点击"今日辟谣"
        print("4. 查找'今日辟谣'...")
        
        rumor_selectors = [
            "text=今日辟谣",
            "a:has-text('今日辟谣')",
            "[href*='辟谣']",
            "//a[contains(text(), '今日辟谣')]",
            "//a[contains(text(), '辟谣')]",
            ".channel-item:has-text('今日辟谣')",
            "[title*='今日辟谣']"
        ]
        
        rumor_found = False
        for selector in rumor_selectors:
            try:
                print(f"   尝试选择器: {selector}")
                if selector.startswith("//"):
                    await page.wait_for_selector(f"xpath={selector}", timeout=3000)
                    await page.click(f"xpath={selector}")
                else:
                    await page.wait_for_selector(selector, timeout=3000)
                    await page.click(selector)
                
                print(f"✅ 成功点击'今日辟谣' (选择器: {selector})")
                rumor_found = True
                break
            except Exception as e:
                print(f"   失败: {e}")
                continue
        
        if rumor_found:
            print("✅ 成功进入'今日辟谣'页面")
        else:
            print("⚠️ 未找到'今日辟谣'，停留在新闻页面")
        
        # 等待页面加载
        await asyncio.sleep(3)
        
        # 5. 给最终页面添加元素标记
        print("5. 给最终页面添加元素标记...")
        
        # 重新执行标记代码（因为页面已经改变）
        final_result = await page.evaluate("""
            () => {
                // 移除之前的标记
                const existingMarkers = document.querySelectorAll('.element-marker');
                existingMarkers.forEach(marker => marker.remove());
                
                // 重新添加样式（如果不存在）
                if (!document.querySelector('style[data-marker-style]')) {
                    const style = document.createElement('style');
                    style.setAttribute('data-marker-style', 'true');
                    style.textContent = `
                        .element-marker {
                            position: absolute;
                            border: 2px solid red;
                            background-color: rgba(255, 0, 0, 0.1);
                            pointer-events: none;
                            z-index: 10000;
                            box-sizing: border-box;
                        }
                        
                        .element-number {
                            position: absolute;
                            top: -15px;
                            left: -2px;
                            background-color: red;
                            color: white;
                            font-size: 12px;
                            font-weight: bold;
                            padding: 2px 6px;
                            border-radius: 3px;
                            min-width: 20px;
                            text-align: center;
                            font-family: Arial, sans-serif;
                        }
                    `;
                    document.head.appendChild(style);
                }
                
                // 重新标记元素
                const elementsToMark = [];
                
                // 标题
                document.querySelectorAll('h1, h2, h3, h4, h5, h6, .title, [class*="title"]').forEach(el => {
                    if (el.offsetWidth > 0 && el.offsetHeight > 0) {
                        elementsToMark.push({element: el, type: 'title'});
                    }
                });
                
                // 图片
                document.querySelectorAll('img').forEach(el => {
                    if (el.offsetWidth > 30 && el.offsetHeight > 30) {
                        elementsToMark.push({element: el, type: 'image'});
                    }
                });
                
                // 链接
                document.querySelectorAll('a').forEach(el => {
                    const text = (el.innerText || '').trim();
                    if (text && el.offsetWidth > 0 && el.offsetHeight > 0 && text.length > 2) {
                        elementsToMark.push({element: el, type: 'link'});
                    }
                });
                
                // 限制数量并添加标记
                const selectedElements = elementsToMark.slice(0, 40);
                selectedElements.forEach((item, index) => {
                    const element = item.element;
                    const rect = element.getBoundingClientRect();
                    
                    const marker = document.createElement('div');
                    marker.className = 'element-marker';
                    marker.style.left = (rect.left + window.scrollX) + 'px';
                    marker.style.top = (rect.top + window.scrollY) + 'px';
                    marker.style.width = rect.width + 'px';
                    marker.style.height = rect.height + 'px';
                    
                    const number = document.createElement('div');
                    number.className = 'element-number';
                    number.textContent = index + 1;
                    marker.appendChild(number);
                    
                    document.body.appendChild(marker);
                });
                
                return {
                    totalElements: selectedElements.length,
                    pageTitle: document.title
                };
            }
        """)
        
        print(f"✅ 最终页面标记完成，共标记 {final_result['totalElements']} 个元素")
        print(f"当前页面: {final_result['pageTitle']}")
        
        print("\n🎉 测试完成！浏览器将保持打开状态供您查看效果...")
        print("按 Ctrl+C 退出")
        
        # 保持浏览器打开
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            print("\n用户中断，关闭浏览器...")
        
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if browser:
            await browser.close()
        if playwright:
            await playwright.stop()


def main():
    """主函数"""
    print("更新后的百度自动化功能测试")
    print("=" * 50)
    print("测试流程:")
    print("1. 打开百度首页")
    print("2. 给百度首页元素添加红框和序号标记")
    print("3. 点击'新闻'链接")
    print("4. 点击'今日辟谣'")
    print("5. 给最终页面元素添加标记")
    print("=" * 50)
    
    try:
        asyncio.run(test_updated_baidu_flow())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    main()
