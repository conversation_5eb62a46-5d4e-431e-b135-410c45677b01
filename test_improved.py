#!/usr/bin/env python3
"""
测试改进后的浏览器自动化工具
验证新的元素查找功能
"""

import asyncio
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from simple_browser_automation import BrowserWorker


async def test_page_analysis():
    """测试页面分析功能"""
    print("测试页面分析功能...")
    
    worker = BrowserWorker()
    
    try:
        # 模拟打开浏览器
        print("启动浏览器...")
        from playwright.async_api import async_playwright
        
        chromium_path = r"C:\Users\<USER>\AppData\Local\ms-playwright\chromium-1155\chrome-win\chrome.exe"
        
        worker.playwright = await async_playwright().start()
        worker.browser = await worker.playwright.chromium.launch(
            executable_path=chromium_path,
            headless=False
        )
        
        worker.page = await worker.browser.new_page()
        
        # 访问测试页面
        test_url = "https://i.zte.com.cn/index/ispace/#/space/f3d7d1b331c445088fe87a3bc56a7dfb/wiki/page/e6adcfa179ee432aab2f65336513374b/view"
        print(f"访问测试页面: {test_url}")
        
        await worker.page.goto(test_url)
        await asyncio.sleep(5)  # 等待页面加载
        
        # 测试页面分析
        print("开始页面分析...")
        
        page_info = await worker.page.evaluate("""
            () => {
                // 获取所有文本内容
                const allText = document.body.innerText || document.body.textContent || '';
                
                // 获取所有按钮和可点击元素
                const clickableElements = [];
                const buttons = document.querySelectorAll('button, [role="button"], .btn, [onclick], a, .clickable, [data-testid], .el-button');
                
                buttons.forEach((el, index) => {
                    const text = (el.innerText || el.textContent || el.title || el.getAttribute('aria-label') || '').trim();
                    const classes = el.className || '';
                    const id = el.id || '';
                    const tagName = el.tagName || '';
                    
                    clickableElements.push({
                        index: index,
                        text: text,
                        classes: classes,
                        id: id,
                        tagName: tagName
                    });
                });
                
                // 查找包含"新建"的元素
                const newElements = [];
                document.querySelectorAll('*').forEach((el, index) => {
                    const text = (el.innerText || el.textContent || '').trim();
                    if (text.includes('新建') || text.includes('新增') || text.includes('创建') || text.includes('添加')) {
                        const rect = el.getBoundingClientRect();
                        newElements.push({
                            index: index,
                            text: text,
                            tagName: el.tagName,
                            classes: el.className || '',
                            id: el.id || '',
                            isVisible: rect.width > 0 && rect.height > 0,
                            x: rect.x,
                            y: rect.y,
                            width: rect.width,
                            height: rect.height
                        });
                    }
                });
                
                return {
                    allText: allText.substring(0, 2000),  // 前2000字符
                    clickableCount: clickableElements.length,
                    clickableElements: clickableElements.slice(0, 20),
                    newElements: newElements.filter(el => el.isVisible).slice(0, 10)
                };
            }
        """)
        
        print(f"\n=== 页面分析结果 ===")
        print(f"页面文本内容 (前2000字符):")
        print(page_info['allText'])
        print(f"\n找到 {page_info['clickableCount']} 个可点击元素")
        print(f"找到 {len(page_info['newElements'])} 个包含'新建'的可见元素")
        
        print(f"\n=== 可点击元素 ===")
        for element in page_info['clickableElements']:
            print(f"[{element['index']}] {element['tagName']}: '{element['text'][:50]}'")
            if element['classes']:
                print(f"    class: {element['classes']}")
            if element['id']:
                print(f"    id: {element['id']}")
        
        print(f"\n=== 包含'新建'的元素 ===")
        for element in page_info['newElements']:
            print(f"[{element['index']}] {element['tagName']}: '{element['text'][:50]}'")
            print(f"    位置: ({element['x']:.0f}, {element['y']:.0f}) 大小: {element['width']:.0f}x{element['height']:.0f}")
            if element['classes']:
                print(f"    class: {element['classes']}")
            if element['id']:
                print(f"    id: {element['id']}")
        
        # 清理
        await worker.browser.close()
        await worker.playwright.stop()
        
        print("\n✅ 页面分析测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        if worker.browser:
            await worker.browser.close()
        if worker.playwright:
            await worker.playwright.stop()


def main():
    """主函数"""
    print("改进版浏览器自动化工具 - 页面分析测试")
    print("=" * 60)
    
    try:
        asyncio.run(test_page_analysis())
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"测试出错: {e}")


if __name__ == "__main__":
    main()
