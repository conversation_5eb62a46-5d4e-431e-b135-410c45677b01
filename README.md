# 浏览器自动化工具

这是一个基于PySide6和Playwright的浏览器自动化工具，可以记录和重放用户的浏览器操作。

## 功能特性

1. **打开浏览器**: 使用指定的Chromium浏览器打开网页
2. **关闭浏览器**: 安全关闭浏览器实例
3. **记录人工操作**: 记录用户在浏览器中的操作（点击、导航等）
4. **模仿人工操作**: 重放之前记录的操作序列

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行主程序：
```bash
python browser_automation.py
```

2. 界面说明：
   - **等待时间设置**: 设置打开网页后的等待时间（默认5秒）
   - **打开浏览器**: 打开浏览器并访问示例网址
   - **关闭浏览器**: 关闭当前浏览器实例
   - **记录人工操作**: 开始/停止记录用户操作
   - **模仿人工操作**: 重放最近记录的操作

3. 操作流程：
   - 点击"打开浏览器"打开网页
   - 点击"记录人工操作"开始记录
   - 在浏览器中进行各种操作（点击、导航等）
   - 再次点击"记录人工操作"停止记录并保存
   - 点击"模仿人工操作"重放记录的操作

## 文件结构

```
browser/
├── browser_automation.py  # 主程序文件
├── requirements.txt       # 依赖包列表
├── configs/              # 存储记录文件的文件夹
│   └── recorded_actions_*.yaml  # 记录的操作文件
└── README.md            # 说明文档
```

## 配置说明

- **浏览器路径**: 程序使用指定的Chromium路径，如需修改请编辑代码中的`chromium_path`变量
- **示例网址**: 默认打开`https://www.example.com`，可在代码中修改
- **记录文件**: 操作记录保存在`configs/`文件夹下的YAML文件中

## 注意事项

1. 确保指定的Chromium浏览器路径存在且可执行
2. 记录操作时，程序会监听点击和导航事件
3. 重放操作时会按照记录的时间顺序执行
4. 所有操作都在独立线程中执行，不会阻塞界面

## 技术栈

- **PySide6**: GUI界面框架
- **Playwright**: 浏览器自动化库
- **PyYAML**: YAML文件处理
- **asyncio**: 异步操作支持
